# 🚀 SoftProxy - HTTP/HTTPS 代理拦截器

一个强大的Windows代理拦截工具，可以拦截任意软件的HTTP/HTTPS请求并修改响应内容。

## ✨ 核心特性

- 🔧 **进程注入** - 使用proxinject强制任意Windows程序使用代理
- 🌐 **HTTP拦截** - 完全拦截和修改HTTP请求/响应内容
- 🔒 **HTTPS监控** - 监控HTTPS连接流量（隧道模式）

- 📊 **实时监控** - 实时显示连接状态和流量统计
- ⚙️ **规则配置** - 灵活的响应修改规则配置系统

## 🎯 系统概述

这个系统结合了 **proxinject** 和自定义Python代理服务器，可以拦截任意Windows程序的HTTP请求并修改响应内容。

### 核心组件
1. **proxinject**: Windows进程注入工具，强制程序使用SOCKS5代理
2. **增强版代理服务器**: 接收SOCKS5连接，拦截HTTP请求，修改响应内容

### 工作原理
```
目标程序 → proxinject注入 → SOCKS5代理 → HTTP拦截/修改 → 目标服务器
                                    ↓
                              实时监控和日志
```

## 🏗️ 项目结构

```
softproxy/
├── 📄 核心文件
│   ├── enhanced_proxy_server.py    # 增强版代理服务器（支持HTTP/HTTPS）
│   ├── proxy_ui.py                 # 简洁实用的UI界面
│   └── test_app.py                 # 简单测试程序
│
├── 🔧 注入工具（已提取到根目录）
│   ├── proxinjector-cli.exe        # 命令行注入工具
│   ├── proxinjectee.dll           # 64位注入DLL
│   ├── proxinjectee32.dll         # 32位注入DLL
│   └── wow64-address-dumper.exe   # WoW64地址转储工具
│
├── 📚 文档文件
│   └── README.md                   # 完整项目说明（本文件）
│
└── 📁 原始工具目录
    └── proxinject/                 # proxinject完整工具包
        ├── LICENSE                 # proxinject许可证
        ├── README.md              # proxinject说明
        ├── docs/                  # 文档和截图
        ├── resources/             # 图标资源
        └── proxinject-tools/      # 完整编译包
```

## 🚀 快速开始

### 环境要求
- Windows 10/11
- Python 3.9+
- 管理员权限（用于进程注入）

### 安装依赖
```bash
pip install requests customtkinter psutil
```

### 启动方式

#### 方法1: 使用UI界面（推荐）
```bash
python proxy_ui.py
```
点击 "🎯 一键启动" 按钮即可自动完成所有操作。

#### 方法2: 手动启动代理服务器
1. **启动代理服务器**
```bash
python enhanced_proxy_server.py
```

2. **注入目标进程**
```bash
# 使用根目录下的可执行文件
.\proxinjector-cli.exe -n "目标程序" -p 127.0.0.1:1080 -l
```

## 📋 详细使用步骤

### 1. 配置要修改的响应内容

在 `enhanced_proxy_server.py` 中修改 `response_modifications` 字典：

```python
self.response_modifications = {
    'httpbin.org': {
        'replace_content': True,
        'new_content': '{"message": "Hello from custom proxy!", "modified": true}'
    },
    'api.github.com': {
        'replace_content': True,
        'new_content': '{"custom": "Modified GitHub API response"}'
    }
}
```

### 2. 启动代理服务器

```bash
python enhanced_proxy_server.py
```

服务器将在 `127.0.0.1:1080` 启动并显示：
```
INFO - 增强版SOCKS5代理服务器启动在 127.0.0.1:1080
INFO - HTTP修改规则: ['httpbin.org', 'api.github.com']
INFO - HTTPS拦截域名: ['124.160.144.209']
```

### 3. 注入目标进程

#### 按进程名注入
```bash
.\proxinjector-cli.exe -n "智赞助手" -p 127.0.0.1:1080 -l
```

#### 按PID注入
```bash
.\proxinjector-cli.exe -i 1234 -p 127.0.0.1:1080 -l
```

#### 启动新进程并注入
```bash
.\proxinjector-cli.exe -e "python test_app.py" -p 127.0.0.1:1080 -l -w
```

### 4. 观察结果

- 代理服务器会显示拦截到的HTTP请求
- 匹配规则的响应内容会被修改
- 目标程序会收到修改后的响应

## 🧪 测试示例

### 创建测试程序

```python
# test_app.py
import requests
import time

def test_requests():
    urls = [
        'http://httpbin.org/json',
        'http://httpbin.org/get',
        'https://api.github.com/users/octocat'
    ]

    for url in urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"URL: {url}")
            print(f"Status: {response.status_code}")
            print(f"Content: {response.text[:200]}...")
            print("-" * 50)
        except Exception as e:
            print(f"Error: {e}")
        time.sleep(1)

if __name__ == '__main__':
    test_requests()
```

### 运行测试

1. 启动代理服务器：`python enhanced_proxy_server.py`
2. 注入测试程序：
```bash
.\proxinjector-cli.exe -e "python test_app.py" -p 127.0.0.1:1080 -l -w
```



## 🔧 高级配置

### 动态添加修改规则

```python
from enhanced_proxy_server import EnhancedProxyServer

proxy = EnhancedProxyServer()
proxy.add_modification_rule('new-domain.com', '{"modified": true}')
```

### 复杂的响应修改

可以在 `modify_http_response` 方法中实现：
- 基于请求URL的条件修改
- JSON内容的部分修改
- 添加或删除HTTP头
- 处理压缩内容

### 支持的注入参数

- `-n, --name`: 按进程名匹配（支持通配符）
- `-i, --pid`: 按进程ID匹配
- `-P, --path`: 按完整路径匹配
- `-r, --name-regexp`: 按进程名正则表达式匹配
- `-R, --path-regexp`: 按路径正则表达式匹配
- `-e, --exec`: 启动新进程并注入
- `-l, --enable-log`: 启用详细日志
- `-p, --set-proxy`: 设置代理地址
- `-w, --new-console-window`: 为新进程创建控制台窗口
- `-s, --subprocess`: 注入子进程

## 🎯 应用场景

- **API测试** - 模拟不同的API响应
- **开发调试** - 修改第三方API返回的数据
- **网络分析** - 拦截和分析HTTP流量
- **功能测试** - 测试程序对异常响应的处理
- **逆向工程** - 分析软件的网络行为

## 🛠️ 故障排除

### 常见问题

1. **注入失败**
   - 确保以管理员权限运行
   - 检查目标进程是否存在
   - 验证proxinjector-cli.exe路径是否正确

2. **响应没有被修改**
   - 检查域名匹配规则
   - 确认代理服务器正常运行
   - 验证目标程序确实通过了代理

3. **连接超时**
   - 检查防火墙设置
   - 确认端口1080没有被占用
   - 验证网络连接

### 调试技巧

1. **启用详细日志**：使用 `-l` 参数
2. **检查代理服务器日志**：观察控制台输出
3. **使用简单的测试程序**：如curl或wget
4. **检查进程列表**：确认目标进程正在运行

## 📊 功能特性

### ✅ 已实现功能

- [x] SOCKS5代理服务器
- [x] HTTP请求/响应拦截和修改
- [x] HTTPS隧道转发和监控
- [x] 进程注入（基于proxinject）

- [x] 实时日志和连接监控
- [x] 灵活的规则配置管理
- [x] 多种进程注入方式
- [x] 流量统计和分析

### 🔄 开发中功能

- [ ] HTTPS内容解密和修改
- [ ] 规则持久化存储
- [ ] 连接统计图表
- [ ] 批量进程管理
- [ ] 插件系统扩展

## ⚠️ 注意事项

- 仅用于合法的测试和开发目的
- 需要管理员权限运行
- 某些防病毒软件可能会报告DLL注入行为
- 在生产环境使用前请充分测试
- 遵守相关法律法规和软件许可协议
- 当前版本主要支持HTTP，HTTPS为隧道模式

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔗 相关项目

- [proxinject](https://github.com/PragmaTwice/proxinject) - Windows进程注入工具

- [Python requests文档](https://docs.python-requests.org/) - HTTP库文档
- [SOCKS5协议规范](https://tools.ietf.org/html/rfc1928) - SOCKS5协议标准

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
