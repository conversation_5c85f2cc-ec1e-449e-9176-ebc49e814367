# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Certificates
*.crt
*.key
*.pem

# Temporary files
*.tmp
*.temp

# Downloaded files
*.zip

# Exclude core proxinject files from ignore (we need these)
!proxinjector-cli.exe
!proxinjectee.dll
!proxinjectee32.dll
!wow64-address-dumper.exe

# But ignore other exe/dll files
*.exe
*.dll

# Build artifacts
proxinject/build/
proxinject/release/
proxinject/proxinject-tools/
proxinject-v*.zip

# Test files
test_output/
temp/

# Account configuration (contains sensitive data)
platform_accounts.json

# Browser data
userData0/

# Configuration files
*.gob
browser.gob
pingtai.gob
runNum.gob
taskSetting.gob

# Temporary test files
test_*.py
debug_*.py

# Nuitka compilation artifacts
*.dist/
*.build/
proxy_ui.dist/
proxy_ui.build/

# Build scripts output
build_*.bat
build_simple.bat
build_nuitka.bat

# License file (contains encrypted license)
license.dat

# Error logs
error_log.txt

# HAR files
*.har
