# 负载均衡设计文档

## 📋 项目概述

将原本只发往熊猫平台的请求，通过负载均衡策略分散到三个平台：
- 🍀 **三叶草平台**
- 🐼 **熊猫平台** 
- 🌊 **四海平台**

## 🎯 核心设计原则

### 🔄 完全代理模式
- **统一拦截**: 拦截所有熊猫平台请求，不直接转发
- **负载均衡选择**: 根据策略选择目标平台（包括熊猫平台本身）
- **统一处理**: 无论选择哪个平台，都通过我们的代码调用对应API
- **透明返回**: 将所有平台响应统一转换为熊猫格式返回

### 🗂️ 任务ID映射策略
- **直接使用**: 使用目标平台返回的任务ID，不生成虚拟ID
- **简单映射**: 只记录 `任务ID → 平台名称` 的映射关系
- **提交匹配**: 提交时根据任务ID找到对应平台

## 📊 平台API对比

| 项目 | 熊猫平台 | 三叶草平台 | 四海平台 |
|------|----------|------------|----------|
| **获取任务** | GET `/studio/api/task/get` | GET `/pull` | POST `/order/selectOneTask` |
| **提交结果** | GET `/studio/api/task/submit` | GET `/push` | POST `/order/taskSubmit` |
| **认证方式** | URL参数`key` | URL参数`key` | Header `Token` |
| **成功状态码** | `code: 0` | `code: 0` | `code: 1` |
| **任务ID字段** | `studiotask_id` | `task_id` | `taskLogId` |

## ⚠️ 重要说明

### 🎯 熊猫平台的特殊处理
- **统一拦截**: 所有对熊猫平台的请求都会被拦截，包括负载均衡选择到熊猫平台的情况
- **重新发起**: 当负载均衡选择熊猫平台时，我们需要重新发起对熊猫平台的HTTP请求
- **避免循环**: 重新发起的请求要避免再次被代理拦截，直接访问熊猫平台API
- **格式统一**: 熊猫平台的响应已经是标准格式，直接返回即可

### 🔄 三平台统一处理流程
```
原始请求 → 代理拦截 → 负载均衡选择 → 调用对应平台API → 格式转换 → 返回响应

选择熊猫: 拦截 → 选择熊猫 → 直接调用熊猫API → 直接返回
选择三叶草: 拦截 → 选择三叶草 → 调用三叶草API → 转换为熊猫格式 → 返回
选择四海: 拦截 → 选择四海 → 调用四海API → 转换为熊猫格式 → 返回
```

## 🔄 核心流程设计

### 📥 获取任务流程

```
1. 拦截熊猫请求
   原始: GET /studio/api/task/get?key=xxx&platform=dy&type=dz&uid=xxx&sec_uid=xxx

2. 负载均衡选择目标平台
   - 平台过滤: 只从启用的平台中选择
   - 优先级轮询: 主平台 → 其他启用平台轮询
   - 按比例分配: 根据启用平台的权重比例选择

3. 调用目标平台API
   熊猫: GET 112.74.176.127:8020/studio/api/task/get?key=xxx&platform=dy&type=dz&uid=xxx&sec_uid=xxx
   三叶草: GET /pull?key=xxx&uid=xxx&sec_uid=xxx&type=dz
   四海: POST /order/selectOneTask + Token + platform=2&platformType=11&uid=xxx&uidType=2

4. 记录任务映射
   task_mapping[返回的任务ID] = 平台名称

5. 转换响应格式为熊猫格式
   熊猫平台: 直接返回原始响应（已经是熊猫格式）
   其他平台: 转换为熊猫格式 {"success": true, "code": 0, "data": {...}, "msg": "success"}
```

### 📤 提交结果流程

```
1. 拦截熊猫提交请求
   原始: GET /studio/api/task/submit?studiotask_id=xxx&key=xxx&...

2. 查找任务来源
   platform = task_mapping[studiotask_id]

3. 调用对应平台提交API
   熊猫: GET 112.74.176.127:8020/studio/api/task/submit?studiotask_id=xxx&key=xxx&...
   三叶草: GET /push?key=xxx&task_id=xxx&...
   四海: POST /order/taskSubmit + Token + taskLogId=xxx&status=1

4. 统一返回成功响应
   熊猫平台: 直接返回原始响应
   其他平台: 统一返回 {"success": true, "code": 0, "data": null, "msg": ""}
```

## 🛠️ 参数处理规则

### ✅ 保持不变的参数
- **type**: 保持固定值（熊猫:`dz`, 三叶草:`dz`, 四海:固定`platform=2&platformType=11`）
- **uid/sec_uid**: 直接传递，保持一致
- **key/token**: 使用各平台对应的认证信息

### 🔄 字段映射规则

#### 获取任务响应映射
```
熊猫格式 ← 三叶草格式:
studiotask_id ← task_id
params.video_id ← video_id  
params.share_url ← share_url
params.uid ← uid
params.sec_uid ← sec_uid

熊猫格式 ← 四海格式:
studiotask_id ← taskLogId
params.video_id ← video_id (从URL提取)
params.share_url ← shortUrl
params.uid ← uid
params.sec_uid ← secUid
params.price ← orderReceivePrice (转换格式)
```

## 🚫 错误处理策略

### 📋 简化处理原则
- **其他平台异常** → 统一返回"无任务"
- **提交结果异常** → 统一返回"提交成功"
- **访问频繁** → 不特殊处理
- **未知错误** → 降级到"无任务"

### 📝 错误日志记录
为了后续完善错误映射，需要详细记录所有异常情况：

#### 日志文件: `error_log.txt`
记录格式：
```
[时间戳] [平台名称] [操作类型] [错误类型]
请求URL: xxx
请求参数: xxx
响应状态码: xxx
响应内容: xxx
错误处理: 返回无任务/提交成功
---分隔线---
```

#### 记录时机
- **其他平台API调用失败**
- **响应格式无法解析**
- **响应状态码异常**
- **必要字段缺失**
- **网络超时或连接错误**

### 📝 标准错误响应

#### 无任务响应
```json
{
  "success": false,
  "code": 406,
  "data": {},
  "msg": "无任务"
}
```

#### 提交成功响应
```json
{
  "success": true,
  "code": 0,
  "data": null,
  "msg": ""
}
```

### 📋 错误日志示例

#### 获取任务错误日志
```
[2025-01-22 15:30:25] [三叶草] [获取任务] [响应异常]
请求URL: http://www.sanyecao.co:98/pull
请求参数: key=PDb96567483e354c01bf574d3fac0a457a&uid=123456&sec_uid=abcdef&type=dz
响应状态码: 200
响应内容: {"code": 1001, "msg": "账号余额不足", "data": null}
错误处理: 返回无任务
---分隔线---
```

#### 提交结果错误日志
```
[2025-01-22 15:35:10] [四海] [提交结果] [网络超时]
请求URL: http://meetspace.top:2095/order/taskSubmit
请求参数: taskLogId=af252db46cc0&status=1
请求头: Token=13ea0139-5be3-4ff8-b140-06a8015a0014
响应状态码: 超时
响应内容: 请求超时
错误处理: 返回提交成功
---分隔线---
```

## 🎯 负载均衡策略

### 🔧 平台启用控制
- **启用选择**: 可以选择启用/禁用任意平台
- **至少一个**: 必须至少启用一个平台，否则自动启用熊猫平台
- **动态调整**: 可以在运行时动态启用/禁用平台
- **负载均衡**: 只有启用的平台才会参与负载均衡

#### 平台启用逻辑示例
```
场景1: 启用三叶草+熊猫，禁用四海
负载均衡时只会在三叶草和熊猫之间选择

场景2: 只启用熊猫平台
所有请求都会转发到熊猫平台

场景3: 用户尝试禁用所有平台
系统自动启用熊猫平台，防止无平台可用

场景4: 主平台被禁用
优先级轮询策略会跳过主平台，直接轮询其他启用平台
```

### 📊 策略1: 优先级轮询
- **主平台优先**: 优先获取主平台任务（仅限启用的平台）
- **无任务切换**: 主平台无任务时，轮询其他启用的平台
- **平分轮询**: 其他启用平台按顺序轮询

### ⚖️ 策略2: 按比例分配
- **权重设置**: 三叶草:熊猫:四海 = 4:3:1 (可配置)
- **启用过滤**: 只有启用的平台权重才生效
- **轮询算法**: 按启用平台的权重生成轮询序列
- **循环执行**: 按序列循环分配请求

## 🗂️ 数据结构设计

### 任务映射表
```python
task_mapping = {
    "1946223005573550081": "clover",    # 三叶草任务
    "af252db46cc0": "sihai",            # 四海任务
    "1947470341668343808": "panda"      # 熊猫任务
}
```

### 负载均衡状态
```python
load_balancer_state = {
    "strategy": "priority",              # priority | ratio
    "main_platform": "panda",           # 主平台
    "platform_enabled": {               # 平台启用状态
        "clover": True,
        "panda": True,
        "sihai": False                   # 四海平台已禁用
    },
    "weights": {
        "clover": 4,
        "panda": 3,
        "sihai": 1
    },
    "current_sequence": ["clover", "clover", "clover", "clover", "panda", "panda", "panda"],  # 只包含启用的平台
    "sequence_index": 0,
    "request_counts": {
        "clover": 0,
        "panda": 0,
        "sihai": 0
    }
}
```

## 🔧 技术实现要点

### 🌐 请求拦截
- **拦截目标**: `112.74.176.127:8020` 的所有请求
- **识别方法**: 通过URL路径区分获取任务和提交结果
- **拦截位置**: 在代理服务器层面实现

### 🔄 API调用
- **HTTP客户端**: 使用requests库
- **超时设置**: 统一10秒超时
- **错误重试**: 失败时可考虑降级到其他平台

### 💾 状态管理
- **内存存储**: 任务映射表存储在内存中
- **配置持久化**: 负载均衡配置保存到JSON文件
- **状态重置**: 程序重启时清空任务映射表

### 📝 日志管理
- **错误日志**: 所有异常情况记录到`error_log.txt`
- **日志格式**: 时间戳 + 平台 + 操作 + 详细信息
- **日志轮转**: 考虑文件大小限制，避免日志文件过大
- **编码格式**: UTF-8编码，支持中文错误信息

## 🚀 实现优先级

1. **UI界面完善** ✅ - 已完成
2. **负载均衡算法** - 实现两种策略的核心逻辑
3. **请求拦截器** - 在代理服务器中集成拦截功能
4. **API转换器** - 实现各平台API的请求/响应转换
5. **任务映射管理** - 实现任务ID的映射和查找
6. **错误处理** - 实现统一的错误处理机制
7. **测试验证** - 完整流程测试

## 📝 注意事项

- **任务ID唯一性**: 确保不同平台的任务ID不会冲突
- **映射表清理**: 考虑定期清理过期的任务映射
- **并发安全**: 多线程环境下的数据安全
- **日志记录**: 详细记录负载均衡的决策过程和所有错误情况
- **监控统计**: 实时统计各平台的请求分配情况
- **错误分析**: 定期分析error_log.txt，完善错误映射规则
- **日志维护**: 定期清理或归档过大的日志文件
