#!/usr/bin/env python3
"""
SoftProxy 加密许可证系统
用于验证加密许可证并进行网络时间验证
"""

import requests
import json
import hashlib
import base64
from datetime import datetime
import os

class LicenseSystem:
    def __init__(self):
        self.license_file = "license.dat"
        self.secret_key = "SoftProxy2025SecretKey_DoNotShare"
        self.app_signature = "SOFTPROXY_V1"
    
    def get_network_time(self):
        """获取网络时间"""
        time_servers = [
            # 国内可用的时间API
            "http://quan.suning.com/getSysTime.do",
            "https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp",
            # 国外时间API
            "http://worldtimeapi.org/api/timezone/Asia/Shanghai",
            "https://worldtimeapi.org/api/timezone/Asia/Shanghai",
            "http://worldtimeapi.org/api/timezone/Etc/UTC",
            # 备用API
            "https://timeapi.io/api/Time/current/zone?timeZone=Asia/Shanghai",
        ]

        for server in time_servers:
            try:
                print(f"尝试连接: {server}")
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                response = requests.get(server, timeout=5, headers=headers)

                if response.status_code == 200:
                    # 处理苏宁时间API
                    if 'suning.com' in server:
                        data = response.json()
                        if 'sysTime2' in data:
                            time_str = data['sysTime2']
                            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

                    # 处理淘宝时间API
                    elif 'taobao.com' in server:
                        data = response.json()
                        if 'data' in data and 't' in data['data']:
                            timestamp = int(data['data']['t']) / 1000
                            return datetime.fromtimestamp(timestamp)

                    # 处理WorldTimeAPI
                    elif 'worldtimeapi.org' in server:
                        data = response.json()
                        if 'datetime' in data:
                            time_str = data['datetime']
                            if '+' in time_str:
                                time_str = time_str.split('+')[0]
                            elif 'T' in time_str and '.' in time_str:
                                time_str = time_str.split('.')[0]
                            network_time = datetime.fromisoformat(time_str.replace('T', ' '))
                            print(f"✅ 获取网络时间成功: {network_time}")
                            return network_time

                    # 处理TimeAPI.io
                    elif 'timeapi.io' in server:
                        data = response.json()
                        if 'dateTime' in data:
                            time_str = data['dateTime']
                            if 'T' in time_str:
                                if '.' in time_str:
                                    time_str = time_str.split('.')[0]
                                if '+' in time_str:
                                    time_str = time_str.split('+')[0]
                                if 'Z' in time_str:
                                    time_str = time_str.replace('Z', '')
                                network_time = datetime.fromisoformat(time_str.replace('T', ' '))
                                print(f"✅ 获取网络时间成功: {network_time}")
                                return network_time

            except Exception as e:
                print(f"❌ {server} 连接失败: {e}")
                continue

        print("❌ 所有时间服务器连接失败")
        return None
    
    def _create_signature(self, data):
        """创建数据签名"""
        combined = f"{data}{self.secret_key}"
        return hashlib.sha256(combined.encode()).hexdigest()[:32]
    
    def decrypt_license(self, encrypted_license):
        """解密许可证"""
        try:
            # Base64解码
            decoded = base64.b64decode(encrypted_license.encode()).decode()
            
            # 分离数据和签名
            if '|' not in decoded:
                return None, "许可证格式错误"
            
            data_str, signature = decoded.rsplit('|', 1)
            
            # 验证签名
            expected_signature = self._create_signature(data_str)
            if signature != expected_signature:
                return None, "许可证签名验证失败"
            
            # 解析数据
            license_data = json.loads(data_str)
            
            # 验证必需字段
            required_fields = ['app', 'expire_date', 'signature']
            for field in required_fields:
                if field not in license_data:
                    return None, f"许可证缺少必需字段: {field}"
            
            # 验证应用签名
            if license_data['signature'] != self.app_signature:
                return None, "许可证不适用于此应用"
            
            return license_data, "许可证解密成功"
            
        except Exception as e:
            return None, f"许可证解析错误: {e}"
    
    def load_license(self):
        """加载许可证文件"""
        try:
            if not os.path.exists(self.license_file):
                return None, "许可证文件不存在"
            
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_license = f.read().strip()
            
            if not encrypted_license:
                return None, "许可证文件为空"
            
            return encrypted_license, "许可证文件加载成功"
            
        except Exception as e:
            return None, f"读取许可证文件失败: {e}"
    
    def validate_license(self):
        """验证许可证"""
        print("正在验证许可证...")
        
        # 加载许可证文件
        encrypted_license, load_msg = self.load_license()
        if encrypted_license is None:
            return False, load_msg
        
        # 解密许可证
        license_data, decrypt_msg = self.decrypt_license(encrypted_license)
        if license_data is None:
            return False, decrypt_msg
        
        # 获取网络时间
        print("正在获取网络时间...")
        network_time = self.get_network_time()
        if network_time is None:
            return False, "无法获取网络时间，请检查网络连接"
        
        print(f"网络时间: {network_time}")
        
        # 检查到期时间
        try:
            expire_time = datetime.strptime(license_data['expire_date'], '%Y-%m-%d')
            
            if network_time.date() > expire_time.date():
                days_expired = (network_time.date() - expire_time.date()).days
                return False, f"许可证已过期 {days_expired} 天（到期日期：{license_data['expire_date']}）"
            
            days_remaining = (expire_time.date() - network_time.date()).days
            
            # 过期提醒
            if days_remaining <= 7:
                print(f"⚠️ 许可证将在 {days_remaining} 天后过期")
            
            return True, f"许可证验证成功，剩余 {days_remaining} 天"
            
        except Exception as e:
            return False, f"日期解析错误: {e}"
    
    def get_license_info(self):
        """获取许可证信息"""
        encrypted_license, load_msg = self.load_license()
        if encrypted_license is None:
            return None
        
        license_data, decrypt_msg = self.decrypt_license(encrypted_license)
        if license_data is None:
            return None
        
        try:
            expire_time = datetime.strptime(license_data['expire_date'], '%Y-%m-%d')
            current_time = datetime.now()
            days_remaining = (expire_time.date() - current_time.date()).days
            
            return {
                'expire_date': license_data['expire_date'],
                'days_remaining': max(0, days_remaining),
                'is_expired': days_remaining < 0,
                'user': license_data.get('user', 'Unknown'),
                'created': license_data.get('created', 'Unknown')
            }
        except:
            return None

def validate_license():
    """简单的许可证验证函数，供主程序调用"""
    license_system = LicenseSystem()
    is_valid, message = license_system.validate_license()
    
    if not is_valid:
        print(f"❌ {message}")
        return False
    else:
        print(f"✅ {message}")
        return True

if __name__ == "__main__":
    # 测试验证
    license_system = LicenseSystem()
    is_valid, message = license_system.validate_license()
    print(f"\n验证结果: {message}")
    
    # 显示许可证信息
    info = license_system.get_license_info()
    if info:
        print(f"\n许可证信息:")
        print(f"到期日期: {info['expire_date']}")
        print(f"剩余天数: {info['days_remaining']}")
        print(f"用户: {info['user']}")
        print(f"创建时间: {info['created']}")
        print(f"是否过期: {'是' if info['is_expired'] else '否'}")
