# 抓包分析文档

本文档记录了各个平台的API接口分析结果，包括请求方式、参数格式、响应结构等。

## 平台概览

目前支持的平台：
- 🐼 **熊猫平台** - 原始流量目标平台
- 🍀 **三叶草平台** - 分流目标平台之一
- 🌊 **四海平台** - 分流目标平台之二

---

## 1. 熊猫平台 (Panda)

### 基本信息
- **平台名称**: 熊猫平台
- **API域名**: `112.74.176.127:8020`
- **协议**: HTTP
- **用途**: 原始流量目标，所有请求默认发往此平台

### 登录/获取KEY接口

#### 请求信息
```bash
curl -X GET 'http://112.74.176.127:8020/apikey?account=*********&pwd=abc168914399'
```

- **请求方式**: `GET`
- **接口路径**: `/apikey`
- **请求参数**:
  - `account`: 用户账号
  - `pwd`: 用户密码

#### 响应格式
```json
{
  "success": true,
  "code": 0,
  "data": {
    "accesskey": "AKID99fd081a6d4cb407309dd1aa788f3040"
  },
  "msg": "success"
}
```

#### 响应字段说明
- `success`: 布尔值，表示请求是否成功
- `code`: 状态码，0表示成功
- `data.accesskey`: 返回的API密钥
- `msg`: 响应消息

#### 测试结果
- ✅ **状态**: 接口正常
- ✅ **响应时间**: < 1秒
- ✅ **KEY格式**: `AKID` + 32位字符串

---

## 2. 三叶草平台 (Clover)

### 基本信息
- **平台名称**: 三叶草平台
- **API域名**: `www.sanyecao.co:98`
- **协议**: HTTP
- **用途**: 分流目标平台，截取部分请求

### 登录/获取KEY接口

#### 请求信息
```bash
curl -X GET 'http://www.sanyecao.co:98/keys?account=*********&password=abc168914399'
```

- **请求方式**: `GET`
- **接口路径**: `/keys`
- **请求参数**:
  - `account`: 用户账号
  - `password`: 用户密码 (注意：参数名与熊猫平台不同)

#### 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "accesskey": "PDb96567483e354c01bf574d3fac0a457a"
  }
}
```

#### 响应字段说明
- `code`: 状态码，0表示成功
- `msg`: 响应消息
- `data.accesskey`: 返回的API密钥

#### 测试结果
- ✅ **状态**: 接口正常
- ✅ **响应时间**: < 2秒
- ✅ **KEY格式**: `PD` + 32位字符串

#### 与熊猫平台的差异
1. **参数名不同**: 密码参数为`password`而非`pwd`
2. **响应格式**: 缺少`success`字段
3. **KEY前缀**: 使用`PD`前缀而非`AKID`

---

## 3. 四海平台 (Sihai)

### 基本信息
- **平台名称**: 四海平台
- **API域名**: `meetspace.top:2095`
- **协议**: HTTP
- **用途**: 分流目标平台，截取部分请求

### 登录接口

#### 请求信息
```bash
curl -X POST 'http://meetspace.top:2095/order/login' \
  -H 'Host: meetspace.top:2095' \
  -H 'User-Agent: Go-http-client/1.1' \
  -H 'Accept-Encoding: gzip' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  --data-urlencode 'username=*********' \
  --data-urlencode 'password=abc168914399' \
  --http1.1
```

- **请求方式**: `POST`
- **接口路径**: `/order/login`
- **Content-Type**: `application/x-www-form-urlencoded`
- **请求参数**:
  - `username`: 用户名
  - `password`: 用户密码

#### 特殊请求头
- `Host: meetspace.top:2095`
- `User-Agent: Go-http-client/1.1`
- `Accept-Encoding: gzip`
- `--http1.1`: 强制使用HTTP/1.1协议

#### 响应格式
```json
{
  "msg": "登录成功",
  "code": 1,
  "token": "13ea0139-5be3-4ff8-b140-06a8015a0014"
}
```

#### 响应字段说明
- `msg`: 响应消息，"登录成功"表示成功
- `code`: 状态码，1表示成功（与其他平台的0不同）
- `token`: 返回的访问令牌（UUID格式）

#### 测试结果
- ✅ **状态**: 接口正常
- ✅ **响应时间**: < 3秒
- ✅ **TOKEN格式**: UUID格式 (xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)

#### 与其他平台的差异
1. **请求方式**: POST而非GET
2. **参数名**: `username`而非`account`
3. **成功状态码**: `code: 1`而非`code: 0`
4. **返回字段**: `token`而非`accesskey`
5. **TOKEN格式**: UUID格式而非前缀+字符串

---

## 平台对比总结

| 项目 | 熊猫平台 | 三叶草平台 | 四海平台 |
|------|----------|------------|----------|
| **请求方式** | GET | GET | POST |
| **API路径** | `/apikey` | `/keys` | `/order/login` |
| **账号参数** | `account` | `account` | `username` |
| **密码参数** | `pwd` | `password` | `password` |
| **成功状态码** | `code: 0` | `code: 0` | `code: 1` |
| **返回字段** | `accesskey` | `accesskey` | `token` |
| **KEY/TOKEN格式** | `AKID` + 32位 | `PD` + 32位 | UUID格式 |
| **特殊要求** | 无 | 无 | 需要特定请求头 |
| **响应时间** | < 1秒 | < 2秒 | < 3秒 |
| **测试状态** | ✅ 正常 | ✅ 正常 | ✅ 正常 |

### 关键差异点
1. **四海平台使用POST请求**，其他平台使用GET
2. **成功状态码不统一**：熊猫和三叶草用0，四海用1
3. **返回的凭证名称不同**：accesskey vs token
4. **凭证格式差异很大**：前缀+字符串 vs UUID

---

## 实现状态

### 已实现功能
- ✅ 熊猫平台KEY获取
- ✅ 三叶草平台KEY获取
- ✅ 四海平台登录接口测试
- ✅ 账号信息保存/加载
- ✅ UI界面集成

### 待实现功能
- ⏳ 四海平台UI集成
- ⏳ 流量分发逻辑
- ⏳ 请求拦截和转发
- ⏳ 多平台TOKEN管理

---

## 技术说明

### API调用实现
- **HTTP客户端**: Python `requests` 库
- **超时设置**: 10秒
- **错误处理**: 完整的异常捕获
- **JSON解析**: 自动处理不同响应格式

### 安全考虑
- **密码存储**: 本地JSON文件存储（已加入.gitignore）
- **网络传输**: HTTP明文传输（平台限制）
- **日志记录**: 密码在日志中显示为`***`

### 更新记录
- `2025-07-22`: 初始文档创建
- `2025-07-22`: 添加熊猫和三叶草平台分析
- `2025-07-22`: 添加四海平台初步信息
