#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
负载均衡器核心模块
实现三平台（熊猫、三叶草、四海）的负载均衡和API转换
"""

import json
import time
import random
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse, parse_qs, urlencode


class LoadBalancer:
    """负载均衡器主类"""
    
    def __init__(self, config_file: str = "load_balancer_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.task_mapping = {}  # 任务ID映射表: task_id -> platform
        self.task_info = {}     # 任务详细信息: task_id -> {platform, uid, sec_uid, type}
        self.sequence_index = 0  # 按比例分配的序列索引
        self.request_counts = {"clover": 0, "panda": 0, "sihai": 0}

        # UI实例引用，用于统计更新
        self.ui_instance = None
        
        # 平台配置
        self.platform_configs = {
            "panda": {
                "name": "熊猫",
                "base_url": "https://xm.wsff.cn",
                "get_task_path": "/studio/api/task/get",
                "submit_task_path": "/studio/api/task/submit",
                "auth_type": "key",
                "key": "AKID99fd081a6d4cb407309dd1aa788f3040"
            },
            "clover": {
                "name": "三叶草", 
                "base_url": "http://www.sanyecao.co:98",
                "get_task_path": "/pull",
                "submit_task_path": "/push",
                "auth_type": "key",
                "key": "PDb96567483e354c01bf574d3fac0a457a"
            },
            "sihai": {
                "name": "四海",
                "base_url": "http://meetspace.top:2095",
                "get_task_path": "/order/selectOneTask",
                "submit_task_path": "/order/taskSubmit",
                "auth_type": "token",
                "token": "13ea0139-5be3-4ff8-b140-06a8015a0014"
            }
        }
    
    def load_config(self) -> Dict:
        """加载负载均衡配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # 默认配置
            return {
                "strategy": "priority",
                "main_platform": "熊猫",
                "platform_enabled": {
                    "clover": True,
                    "panda": True,
                    "sihai": True
                },
                "weights": {
                    "clover": 4,
                    "panda": 3,
                    "sihai": 1
                }
            }
    
    def get_enabled_platforms(self) -> List[str]:
        """获取启用的平台列表"""
        enabled = []
        for platform, enabled_status in self.config["platform_enabled"].items():
            if enabled_status:
                enabled.append(platform)
        return enabled if enabled else ["panda"]  # 至少保证熊猫平台启用
    
    def select_platform(self) -> str:
        """根据负载均衡策略选择平台"""
        enabled_platforms = self.get_enabled_platforms()
        
        if self.config["strategy"] == "priority":
            return self._select_by_priority(enabled_platforms)
        else:  # ratio
            return self._select_by_ratio(enabled_platforms)
    
    def _select_by_priority(self, enabled_platforms: List[str]) -> str:
        """优先级轮询策略"""
        # 将中文平台名转换为英文key
        platform_map = {"三叶草": "clover", "熊猫": "panda", "四海": "sihai"}
        main_platform_key = platform_map.get(self.config["main_platform"], "panda")
        
        # 如果主平台启用，优先选择主平台
        if main_platform_key in enabled_platforms:
            return main_platform_key
        
        # 否则轮询其他启用的平台
        return random.choice(enabled_platforms)
    
    def _select_by_ratio(self, enabled_platforms: List[str]) -> str:
        """按比例分配策略"""
        # 生成权重序列
        sequence = []
        weights = self.config["weights"]
        
        for platform in enabled_platforms:
            weight = weights.get(platform, 1)
            sequence.extend([platform] * weight)
        
        if not sequence:
            return "panda"
        
        # 按序列循环选择
        selected = sequence[self.sequence_index % len(sequence)]
        self.sequence_index += 1
        return selected
    
    def get_task(self, original_params: Dict[str, str]) -> Tuple[Dict, str]:
        """
        获取任务的主入口
        返回: (响应数据, 选择的平台)
        """
        selected_platform = self.select_platform()
        self.request_counts[selected_platform] += 1
        
        try:
            if selected_platform == "panda":
                return self._get_task_panda(original_params), selected_platform
            elif selected_platform == "clover":
                return self._get_task_clover(original_params), selected_platform
            elif selected_platform == "sihai":
                return self._get_task_sihai(original_params), selected_platform
        except Exception as e:
            self._log_error(selected_platform, "获取任务", "异常", str(e), original_params)
            return self._create_no_task_response(), selected_platform
        
        return self._create_no_task_response(), selected_platform
    
    def _get_task_panda(self, params: Dict[str, str]) -> Dict:
        """获取熊猫平台任务"""
        config = self.platform_configs["panda"]
        url = f"{config['base_url']}{config['get_task_path']}"

        print(f"🐼 熊猫平台 -> 请求: {url} 参数: {params}")

        # 直接使用原始参数
        response = requests.get(url, params=params, timeout=10, verify=False)

        print(f"🐼 熊猫平台 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()

            # 熊猫平台响应直接返回，但需要记录任务映射
            if result.get("success") and result.get("data", {}).get("studiotask_id"):
                task_id = result["data"]["studiotask_id"]
                self.task_mapping[task_id] = "panda"

                # 保存任务详细信息
                self.task_info[task_id] = {
                    "platform": "panda",
                    "uid": params.get("uid", ""),
                    "sec_uid": params.get("sec_uid", ""),
                    "type": params.get("type", "dz")
                }

                # 更新UI统计 - 请求任务数+1
                if self.ui_instance:
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("panda", "requests"))
                    # 获取video_id
                    video_id = result.get("data", {}).get("params", {}).get("video_id", "")
                    log_msg = f"熊猫平台获取任务成功: {task_id}"
                    if video_id:
                        log_msg += f" vid: {video_id}"
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("获取任务", log_msg))



            return result
        else:
            print(f"❌ 熊猫平台请求失败")
            return self._create_no_task_response()
    
    def _get_task_clover(self, params: Dict[str, str]) -> Dict:
        """获取三叶草平台任务"""
        config = self.platform_configs["clover"]
        url = f"{config['base_url']}{config['get_task_path']}"
        
        # 转换参数格式
        clover_params = {
            "key": config["key"],
            "uid": params.get("uid", ""),
            "sec_uid": params.get("sec_uid", ""),
            "type": params.get("type", "dz")
        }
        
        print(f"🍀 三叶草平台 -> 请求: {url} 参数: {clover_params}")

        response = requests.get(url, params=clover_params, timeout=10)

        print(f"🍀 三叶草平台 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()
        else:
            print(f"❌ 三叶草平台请求失败")
            return self._create_no_task_response()
        
        # 转换为熊猫格式
        return self._convert_clover_to_panda_response(result, params)
    
    def _get_task_sihai(self, params: Dict[str, str]) -> Dict:
        """获取四海平台任务"""
        config = self.platform_configs["sihai"]
        url = f"{config['base_url']}{config['get_task_path']}"
        
        # 转换参数格式
        sihai_data = {
            "platform": "2",
            "platformType": "11", 
            "uid": params.get("uid", ""),
            "uidType": "2"
        }
        
        headers = {
            "Host": "meetspace.top:2095",
            "User-Agent": "Go-http-client/1.1",
            "Accept-Encoding": "gzip",
            "Token": config["token"],
            "Content-Type": "application/x-www-form-urlencoded"
        }

        print(f"🌊 四海平台 -> 请求: {url} 参数: {sihai_data}")

        response = requests.post(url, data=sihai_data, headers=headers, timeout=10)

        print(f"🌊 四海平台 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()
        else:
            print(f"❌ 四海平台请求失败")
            return self._create_no_task_response()
        
        # 转换为熊猫格式
        return self._convert_sihai_to_panda_response(result, params)
    
    def _convert_clover_to_panda_response(self, clover_response: Dict, params: Dict) -> Dict:
        """将三叶草响应转换为熊猫格式"""
        # 检查是否有任务
        if clover_response.get("code") == 0 and clover_response.get("data"):
            data = clover_response["data"]
            task_id = data.get("task_id")

            if task_id:
                # 记录任务映射
                self.task_mapping[task_id] = "clover"

                # 保存任务详细信息（从原始请求参数中获取，不是响应数据）
                self.task_info[task_id] = {
                    "platform": "clover",
                    "uid": params.get("uid", ""),      # 使用原始请求的uid
                    "sec_uid": params.get("sec_uid", ""),  # 使用原始请求的sec_uid
                    "type": "dz"  # 三叶草固定为dz类型
                }

                # 更新UI统计 - 请求任务数+1
                if self.ui_instance:
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("clover", "requests"))
                    # 获取video_id
                    video_id = data.get("video_id", "")
                    log_msg = f"三叶草平台获取任务成功: {task_id}"
                    if video_id:
                        log_msg += f" vid: {video_id}"
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("获取任务", log_msg))

                # 转换为熊猫格式
                return {
                    "success": True,
                    "code": 0,
                    "data": {
                        "studiotask_id": task_id,
                        "type": "dz",
                        "params": {
                            "price": 0,  # 三叶草没有价格字段
                            "sec_uid": data.get("sec_uid", ""),
                            "share_url": data.get("share_url", ""),
                            "uid": data.get("uid", ""),
                            "video_id": data.get("video_id", "")
                        }
                    },
                    "msg": "success"
                }

        # 检查是否是无任务的情况 (code:999)
        elif clover_response.get("code") == 999:
            # 三叶草返回暂无任务，转换为熊猫格式的无任务响应
            return {
                "success": False,
                "code": 999,
                "data": None,
                "msg": "暂无任务"
            }

        # 其他异常情况也返回无任务
        return self._create_no_task_response()
    
    def _convert_sihai_to_panda_response(self, sihai_response: Dict, params: Dict) -> Dict:
        """将四海响应转换为熊猫格式"""
        if sihai_response.get("code") == 1 and sihai_response.get("data"):
            data = sihai_response["data"]
            task_id = data.get("taskLogId")

            if task_id:
                # 记录任务映射
                self.task_mapping[task_id] = "sihai"

                # 保存任务详细信息（从原始请求参数中获取）
                original_uid = params.get("uid", "")
                original_sec_uid = params.get("sec_uid", "")

                self.task_info[task_id] = {
                    "platform": "sihai",
                    "uid": original_uid,
                    "sec_uid": original_sec_uid,
                    "type": params.get("type", "dz")
                }

                # 更新UI统计 - 请求任务数+1
                if self.ui_instance:
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("sihai", "requests"))
                    # 获取video_id
                    video_id = data.get("video_id", "")
                    log_msg = f"四海平台获取任务成功: {task_id}"
                    if video_id:
                        log_msg += f" vid: {video_id}"
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("获取任务", log_msg))


                
                # 提取并清理四海平台的video_id
                raw_video_id = data.get("video_id", "")
                video_id = self._extract_clean_video_id(raw_video_id)
                
                # 转换为熊猫格式
                return {
                    "success": True,
                    "code": 0,
                    "data": {
                        "studiotask_id": task_id,
                        "type": "dz",
                        "params": {
                            "price": int(float(data.get("orderReceivePrice", "0")) * 100),  # 转换为分
                            "sec_uid": data.get("secUid", ""),
                            "share_url": data.get("shortUrl", ""),
                            "uid": data.get("uid", ""),
                            "video_id": video_id
                        }
                    },
                    "msg": "success"
                }
        
        # 异常情况返回无任务
        return self._create_no_task_response()
    
    def _extract_video_id_from_url(self, url: str) -> str:
        """从四海平台的复杂URL中提取video_id"""
        try:
            # 尝试从URL中提取数字ID
            import re
            match = re.search(r'/note/(\d+)', url)
            if match:
                return match.group(1)
            
            # 如果没找到，尝试其他模式
            match = re.search(r'(\d{19})', url)  # 抖音视频ID通常是19位数字
            if match:
                return match.group(1)
        except:
            pass
        
        return ""

    def _extract_clean_video_id(self, raw_video_id: str) -> str:
        """从四海平台的video_id字段中提取纯数字ID"""
        if not raw_video_id:
            return ""

        import re

        # 四海平台的video_id可能包含：
        # 1. 纯数字: "7528808687566195968"
        # 2. 带参数: "7528808687566195968?previous_page=app_code_link"
        # 3. 带HTML: "7528808687566195968?previous_page=app_code_link\">https://www.douyin.com/note/7528808687566195968?previous_page=app_code_link</a>."

        # 提取最前面的连续数字
        pattern = r'^(\d+)'
        match = re.search(pattern, raw_video_id)
        if match:
            clean_video_id = match.group(1)
            if raw_video_id != clean_video_id:  # 只有当需要清理时才显示日志
                print(f"🌊 四海video_id清理: '{raw_video_id}' -> '{clean_video_id}'")
            return clean_video_id

        # 如果没有匹配到数字，返回空字符串
        print(f"⚠️ 四海video_id无法提取数字: '{raw_video_id}'")
        return ""

    def _create_no_task_response(self) -> Dict:
        """创建无任务响应"""
        return {
            "success": False,
            "code": 406,
            "data": {},
            "msg": "无任务"
        }
    
    def _log_error(self, platform: str, operation: str, error_type: str, error_msg: str, params: Dict = None):
        """记录错误日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        log_entry = f"""[{timestamp}] [{self.platform_configs[platform]['name']}] [{operation}] [{error_type}]
请求参数: {params or {}}
错误信息: {error_msg}
错误处理: 返回无任务
---分隔线---

"""
        
        try:
            with open("error_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
        except:
            pass  # 日志写入失败不影响主流程

    def submit_task(self, original_params: Dict[str, str]) -> Dict:
        """
        提交任务结果的主入口
        """
        task_id = original_params.get("studiotask_id")
        if not task_id:
            return self._create_submit_success_response()

        # 查找任务对应的平台
        platform = self.task_mapping.get(task_id)
        if not platform:
            # 如果找不到映射，默认认为是熊猫平台的任务
            platform = "panda"

        try:
            if platform == "panda":
                return self._submit_task_panda(original_params)
            elif platform == "clover":
                return self._submit_task_clover(original_params, task_id)
            elif platform == "sihai":
                return self._submit_task_sihai(original_params, task_id)
        except Exception as e:
            self._log_error(platform, "提交结果", "异常", str(e), original_params)

        # 清理任务映射
        if task_id in self.task_mapping:
            del self.task_mapping[task_id]

        return self._create_submit_success_response()

    def _submit_task_panda(self, params: Dict[str, str]) -> Dict:
        """提交熊猫平台任务"""
        config = self.platform_configs["panda"]
        url = f"{config['base_url']}{config['submit_task_path']}"

        print(f"🐼 熊猫平台提交 -> 请求: {url} 参数: {params}")

        response = requests.get(url, params=params, timeout=10, verify=False)

        print(f"🐼 熊猫平台提交 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()
            # 检查提交是否成功
            if result.get("success"):
                # 更新UI统计 - 提交成功数+1
                if self.ui_instance:
                    task_id = params.get("studiotask_id", "")
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("panda", "success"))
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("提交任务", f"熊猫平台提交任务成功: {task_id}"))
            return result
        else:
            print(f"❌ 熊猫平台提交失败")
            return self._create_submit_success_response()

    def _submit_task_clover(self, original_params: Dict[str, str], task_id: str) -> Dict:
        """提交三叶草平台任务"""
        config = self.platform_configs["clover"]
        url = f"{config['base_url']}{config['submit_task_path']}"

        # 从保存的任务信息中获取参数
        task_info = self.task_info.get(task_id, {})

        # 转换参数格式
        clover_params = {
            "key": config["key"],
            "uid": task_info.get("uid", ""),
            "sec_uid": task_info.get("sec_uid", ""),
            "type": task_info.get("type", "dz"),
            "task_id": task_id
        }

        print(f"🍀 三叶草平台提交 -> 请求: {url} 参数: {clover_params}")

        response = requests.get(url, params=clover_params, timeout=10)

        print(f"🍀 三叶草平台提交 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()

            # 转换为熊猫格式
            if result.get("code") == 0:
                # 更新UI统计 - 提交成功数+1
                if self.ui_instance:
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("clover", "success"))
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("提交任务", f"三叶草平台提交任务成功: {task_id}"))
                return self._create_submit_success_response()
            else:
                self._log_error("clover", "提交结果", "响应异常", str(result), clover_params)
                return self._create_submit_success_response()
        else:
            return self._create_submit_success_response()

    def _submit_task_sihai(self, original_params: Dict[str, str], task_id: str) -> Dict:
        """提交四海平台任务"""
        config = self.platform_configs["sihai"]
        url = f"{config['base_url']}{config['submit_task_path']}"

        # 从保存的任务信息中获取参数
        task_info = self.task_info.get(task_id, {})

        # 转换参数格式
        sihai_data = {
            "taskLogId": task_id,
            "status": "1"
        }

        print(f"🌊 使用保存的任务信息: {task_info}")

        headers = {
            "Host": "meetspace.top:2095",
            "User-Agent": "Go-http-client/1.1",
            "Accept-Encoding": "gzip",
            "Token": config["token"],
            "Content-Type": "application/x-www-form-urlencoded"
        }

        print(f"🌊 四海平台提交 -> 请求: {url} 参数: {sihai_data}")

        response = requests.post(url, data=sihai_data, headers=headers, timeout=10)

        print(f"🌊 四海平台提交 -> 响应: {response.text}")

        if response.status_code == 200:
            result = response.json()

            # 转换为熊猫格式
            if result.get("code") == 1:
                print(f"✅ 四海平台提交成功")
                # 更新UI统计 - 提交成功数+1
                if self.ui_instance:
                    self.ui_instance.root.after(0, lambda: self.ui_instance.update_platform_stats("sihai", "success"))
                    self.ui_instance.root.after(0, lambda: self.ui_instance.add_log("提交任务", f"四海平台提交任务成功: {task_id}"))
                return self._create_submit_success_response()
            else:
                print(f"❌ 四海平台提交失败: {result}")
                self._log_error("sihai", "提交结果", "响应异常", str(result), sihai_data)
                return self._create_submit_success_response()
        else:
            print(f"❌ 四海平台提交请求失败")
            return self._create_submit_success_response()

    def _create_submit_success_response(self) -> Dict:
        """创建提交成功响应"""
        return {
            "success": True,
            "code": 0,
            "data": None,
            "msg": ""
        }

    def get_status(self) -> Dict:
        """获取负载均衡状态"""
        enabled_platforms = self.get_enabled_platforms()
        platform_names = {"clover": "三叶草", "panda": "熊猫", "sihai": "四海"}

        return {
            "strategy": self.config["strategy"],
            "enabled_platforms": [platform_names[p] for p in enabled_platforms],
            "request_counts": self.request_counts.copy(),
            "task_mapping_count": len(self.task_mapping),
            "sequence_index": self.sequence_index
        }

    def reset_counters(self):
        """重置请求计数"""
        self.request_counts = {"clover": 0, "panda": 0, "sihai": 0}
        self.sequence_index = 0
