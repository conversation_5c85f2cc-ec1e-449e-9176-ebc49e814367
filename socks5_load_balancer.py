#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOCKS5负载均衡代理服务器
基于原enhanced_proxy_server.py，集成负载均衡功能
"""

import socket
import threading
import struct
import select
import re
import json
import time
from urllib.parse import urlparse
import logging
from load_balancer import LoadBalancer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SOCKS5LoadBalancer:
    def __init__(self, host='127.0.0.1', port=1080):
        self.host = host
        self.port = port
        self.server_socket = None
        
        # 创建负载均衡器实例
        self.load_balancer = LoadBalancer()
        
        # 熊猫平台的目标服务器
        self.panda_server = 'xm.wsff.cn'
        self.panda_port = 443  # HTTPS端口
        
        # 需要拦截的API路径
        self.intercept_paths = [
            '/studio/api/task/get',
            '/studio/api/task/submit'
        ]
    
    def start(self):
        """启动SOCKS5代理服务器"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        
        logger.info(f"🚀 SOCKS5负载均衡代理启动在 {self.host}:{self.port}")
        logger.info(f"🎯 拦截目标: {self.panda_server}:{self.panda_port}")
        logger.info(f"⚖️ 负载均衡: 已启用")
        

        
        try:
            while True:
                client_socket, addr = self.server_socket.accept()
                logger.info(f"新连接来自: {addr}")
                
                # 为每个客户端创建新线程
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_socket,)
                )
                client_thread.daemon = True
                client_thread.start()
                
        except KeyboardInterrupt:
            logger.info("SOCKS5代理服务器正在关闭...")
        finally:
            if self.server_socket:
                self.server_socket.close()
    
    def handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            # SOCKS5握手
            if not self.socks5_handshake(client_socket):
                return
            
            # SOCKS5连接请求
            target_host, target_port = self.socks5_connect_request(client_socket)
            if not target_host:
                return
            
            logger.info(f"连接目标: {target_host}:{target_port} ({'HTTPS' if target_port == 443 else 'HTTP'})")
            
            # 检查是否是熊猫平台的请求 (支持HTTP和HTTPS)
            is_panda_http = (target_host == '**************' and target_port == 8020)
            is_panda_https = (target_host == self.panda_server and target_port == self.panda_port)

            if is_panda_http or is_panda_https:
                logger.info(f"🎯 拦截熊猫平台请求: {target_host}:{target_port} -> 转发到 {self.panda_server}")
                self.handle_panda_request(client_socket, target_host, target_port)
            else:
                # 其他请求直接转发
                self.handle_normal_request(client_socket, target_host, target_port)
                
        except Exception as e:
            logger.error(f"处理客户端时出错: {e}")
        finally:
            client_socket.close()
    
    def handle_panda_request(self, client_socket, target_host, target_port):
        """处理熊猫平台请求（需要负载均衡）"""
        try:
            # 发送成功响应给客户端
            self.send_socks5_response(client_socket, True)
            

            
            # 处理HTTP流量，进行负载均衡
            self.handle_load_balanced_http(client_socket, target_host, target_port)
            
        except Exception as e:
            logger.error(f"处理熊猫平台请求失败: {e}")
            self.send_socks5_response(client_socket, False)
    
    def handle_normal_request(self, client_socket, target_host, target_port):
        """处理普通请求（直接转发）"""
        target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            target_socket.connect((target_host, target_port))
            
            # 发送成功响应
            self.send_socks5_response(client_socket, True)
            

            
            # 根据端口判断处理方式
            if target_port == 443:
                # HTTPS连接 - 建立隧道
                self.handle_https_tunnel(client_socket, target_socket, target_host)
            else:
                # HTTP连接 - 直接转发
                self.handle_http_tunnel(client_socket, target_socket, target_host)
            
        except Exception as e:
            logger.error(f"连接目标服务器失败: {e}")
            self.send_socks5_response(client_socket, False)
        finally:
            target_socket.close()
    
    def handle_load_balanced_http(self, client_socket, target_host, target_port):
        """处理负载均衡的HTTP流量"""
        client_buffer = b''
        
        while True:
            try:
                # 接收客户端数据
                data = client_socket.recv(4096)
                if not data:
                    break
                
                client_buffer += data
                
                # 检查是否是完整的HTTP请求
                if b'\r\n\r\n' in client_buffer:
                    # 解析HTTP请求
                    request_lines = client_buffer.split(b'\r\n')
                    if request_lines:
                        request_line = request_lines[0].decode('utf-8', errors='ignore')
                        logger.info(f"📤 HTTP请求: {request_line}")
                        
                        # 解析请求路径
                        parts = request_line.split(' ')
                        if len(parts) >= 2:
                            method = parts[0]
                            path = parts[1]

                            # 检查是否需要负载均衡
                            if any(intercept_path in path for intercept_path in self.intercept_paths):
                                # 需要负载均衡处理 - 让负载均衡器决定使用哪个平台
                                print(f"🔴 拦截原始请求: {method} {path}")
                                response = self.process_load_balanced_request(method, path, client_buffer)
                                client_socket.send(response)


                            else:
                                # 直接转发到熊猫平台新域名（非任务API）
                                print(f"🔵 拦截原始请求: {method} {path}")
                                response = self.forward_to_panda_only(client_buffer)
                                client_socket.send(response)


                    
                    client_buffer = b''
                    
            except Exception as e:
                logger.error(f"处理负载均衡HTTP流量时出错: {e}")
                break
    
    def process_load_balanced_request(self, method, path, request_data):
        """处理需要负载均衡的请求 - 真正调用不同平台的API"""
        try:
            # 解析请求参数
            params = self.parse_http_params(request_data)

            if '/studio/api/task/get' in path:
                # 获取任务请求 - 负载均衡器会自动选择平台并调用对应API
                response_data, selected_platform = self.load_balancer.get_task(params)
                logger.info(f"🎯 负载均衡选择平台: {selected_platform}")



            elif '/studio/api/task/submit' in path:
                # 提交任务请求 - 根据任务ID找到对应平台
                response_data = self.load_balancer.submit_task(params)
                selected_platform = "提交处理"
                logger.info(f"🎯 任务提交处理完成")


            else:
                # 其他请求返回错误
                response_data = {"success": False, "code": 404, "msg": "API not found"}
                selected_platform = "未知"
                logger.warning(f"⚠️ 未知的任务API: {path}")

            # 构造HTTP响应
            json_response = json.dumps(response_data, ensure_ascii=False)
            http_response = f"""HTTP/1.1 200 OK\r
Content-Type: application/json; charset=utf-8\r
Content-Length: {len(json_response.encode('utf-8'))}\r
Connection: close\r
\r
{json_response}"""

            # 显示最终返回给原始请求的响应
            print(f"📤 返回给原始请求的响应: {json_response}")

            return http_response.encode('utf-8')
            
        except Exception as e:
            logger.error(f"负载均衡处理请求失败: {e}")
            error_response = '{"success": false, "code": 500, "msg": "Internal Server Error"}'
            http_response = f"""HTTP/1.1 500 Internal Server Error\r
Content-Type: application/json\r
Content-Length: {len(error_response)}\r
Connection: close\r
\r
{error_response}"""
            return http_response.encode('utf-8')
    
    def forward_to_panda_only(self, request_data):
        """转发非任务API请求到熊猫平台（如登录等）"""
        try:
            logger.info(f"🔄 开始转发到熊猫平台: {self.panda_server}:{self.panda_port}")

            # 使用requests库发送HTTPS请求，更简单可靠
            import requests

            # 解析HTTP请求
            request_str = request_data.decode('utf-8', errors='ignore')
            lines = request_str.split('\r\n')

            if not lines:
                raise Exception("无效的HTTP请求")

            # 解析请求行
            request_line = lines[0]
            parts = request_line.split(' ')
            if len(parts) < 2:
                raise Exception("无效的HTTP请求行")

            method = parts[0]
            path = parts[1]

            # 构建完整URL
            url = f"https://{self.panda_server}{path}"
            logger.info(f"📡 转发URL: {url}")

            # 解析请求头
            headers = {}
            for line in lines[1:]:
                if ':' in line and line.strip():
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()

            # 移除可能冲突的头
            headers.pop('Host', None)
            headers.pop('Content-Length', None)

            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=15, verify=False)
            elif method.upper() == 'POST':
                # 提取POST数据
                body_start = request_str.find('\r\n\r\n')
                post_data = request_str[body_start + 4:] if body_start != -1 else ''
                response = requests.post(url, data=post_data, headers=headers, timeout=15, verify=False)
            else:
                response = requests.request(method, url, headers=headers, timeout=15, verify=False)

            logger.info(f"✅ 熊猫平台响应: {response.status_code}")

            # 构建HTTP响应
            http_response = f"HTTP/1.1 {response.status_code} {response.reason}\r\n"

            # 添加响应头
            for header, value in response.headers.items():
                if header.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                    http_response += f"{header}: {value}\r\n"

            http_response += "\r\n"

            # 添加响应体
            response_data = http_response.encode('utf-8') + response.content

            # 显示返回给原始请求的响应内容
            try:
                response_json = response.json()
                print(f"📤 返回给原始请求的响应: {json.dumps(response_json, ensure_ascii=False)}")
            except:
                # 如果不是JSON格式，显示前200个字符
                response_text = response.text[:200] + ("..." if len(response.text) > 200 else "")
                print(f"📤 返回给原始请求的响应: {response_text}")

            return response_data


        except Exception as e:
            logger.error(f"❌ 转发到熊猫平台失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            error_response = '{"success": false, "code": 500, "msg": "Forward Error"}'
            http_response = f"""HTTP/1.1 500 Internal Server Error\r
Content-Type: application/json\r
Content-Length: {len(error_response)}\r
Connection: close\r
\r
{error_response}"""
            return http_response.encode('utf-8')
    
    def parse_http_params(self, request_data):
        """解析HTTP请求参数"""
        try:
            request_str = request_data.decode('utf-8', errors='ignore')
            lines = request_str.split('\r\n')
            
            if not lines:
                return {}
            
            # 解析请求行
            request_line = lines[0]
            parts = request_line.split(' ')
            if len(parts) < 2:
                return {}
            
            url = parts[1]
            
            # 解析查询参数
            if '?' in url:
                query_string = url.split('?', 1)[1]
                params = {}
                for param in query_string.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        params[key] = value
                return params
            
            return {}
            
        except Exception as e:
            logger.error(f"解析HTTP参数失败: {e}")
            return {}

    def handle_https_tunnel(self, client_socket, target_socket, target_host):
        """处理HTTPS隧道连接"""
        logger.info(f"🔒 建立HTTPS隧道到: {target_host}")

        def forward_data(source, destination, direction):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
                    logger.debug(f"HTTPS {direction}: {len(data)} bytes")
            except Exception as e:
                logger.debug(f"HTTPS转发结束 ({direction}): {e}")

        # 创建双向转发线程
        client_to_server = threading.Thread(
            target=forward_data,
            args=(client_socket, target_socket, "client->server")
        )
        server_to_client = threading.Thread(
            target=forward_data,
            args=(target_socket, client_socket, "server->client")
        )

        client_to_server.daemon = True
        server_to_client.daemon = True

        client_to_server.start()
        server_to_client.start()

        # 等待任一方向的连接结束
        client_to_server.join()
        server_to_client.join()

    def handle_http_tunnel(self, client_socket, target_socket, target_host):
        """处理HTTP隧道连接"""
        logger.info(f"🌐 建立HTTP隧道到: {target_host}")

        def forward_data(source, destination, direction):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
                    logger.debug(f"HTTP {direction}: {len(data)} bytes")
            except Exception as e:
                logger.debug(f"HTTP转发结束 ({direction}): {e}")

        # 创建双向转发线程
        client_to_server = threading.Thread(
            target=forward_data,
            args=(client_socket, target_socket, "client->server")
        )
        server_to_client = threading.Thread(
            target=forward_data,
            args=(target_socket, client_socket, "server->client")
        )

        client_to_server.daemon = True
        server_to_client.daemon = True

        client_to_server.start()
        server_to_client.start()

        # 等待任一方向的连接结束
        client_to_server.join()
        server_to_client.join()

    def socks5_handshake(self, client_socket):
        """SOCKS5握手"""
        try:
            data = client_socket.recv(1024)
            if len(data) < 3 or data[0] != 0x05:
                return False
            client_socket.send(b'\x05\x00')
            return True
        except Exception as e:
            logger.error(f"SOCKS5握手失败: {e}")
            return False

    def socks5_connect_request(self, client_socket):
        """处理SOCKS5连接请求"""
        try:
            data = client_socket.recv(1024)
            if len(data) < 4 or data[0] != 0x05 or data[1] != 0x01:
                return None, None

            addr_type = data[3]

            if addr_type == 0x01:  # IPv4
                host = socket.inet_ntoa(data[4:8])
                port = struct.unpack('>H', data[8:10])[0]
            elif addr_type == 0x03:  # 域名
                domain_len = data[4]
                host = data[5:5+domain_len].decode('utf-8')
                port = struct.unpack('>H', data[5+domain_len:7+domain_len])[0]
            else:
                return None, None

            return host, port

        except Exception as e:
            logger.error(f"解析SOCKS5连接请求失败: {e}")
            return None, None

    def send_socks5_response(self, client_socket, success):
        """发送SOCKS5响应"""
        if success:
            response = b'\x05\x00\x00\x01\x00\x00\x00\x00\x00\x00'
        else:
            response = b'\x05\x01\x00\x01\x00\x00\x00\x00\x00\x00'

        client_socket.send(response)

    def stop(self):
        """停止代理服务器"""
        try:
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
                logger.info("SOCKS5代理服务器已停止")
        except Exception as e:
            logger.error(f"停止代理服务器时出错: {e}")


def main():
    """主函数 - 用于测试"""


    # 创建SOCKS5负载均衡代理
    proxy = SOCKS5LoadBalancer(host='127.0.0.1', port=1080)

    try:
        proxy.start()
    except KeyboardInterrupt:
        print("\nSOCKS5负载均衡代理已停止")
        proxy.stop()


if __name__ == '__main__':
    main()
