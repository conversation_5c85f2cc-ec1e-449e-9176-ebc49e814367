# SoftProxy 加密许可证系统使用说明

## 🔐 **系统概述**

SoftProxy 使用加密许可证系统，在程序启动时读取加密许可证文件，解析出到期时间，并与网络时间进行对比验证。

## 📁 **文件说明**

### **核心文件**
- `license_system.py` - 许可证验证模块
- `license_generator.py` - 许可证生成器
- `license.dat` - 加密许可证文件

### **许可证文件格式**
- **文件名**: `license.dat`
- **格式**: Base64编码的加密字符串
- **内容**: 包含到期日期、用户信息、序列号等

## 🛠️ **生成许可证**

### **基本用法**
```bash
# 生成许可证，到期日期为2025年12月31日
python license_generator.py --expire-date 2025-12-31

# 指定用户名
python license_generator.py --expire-date 2025-12-31 --user "张三"

# 指定输出文件名
python license_generator.py --expire-date 2025-12-31 --user "李四" --output "custom_license.dat"
```

### **生成示例**
```bash
python license_generator.py --expire-date 2026-06-30 --user "VIP用户"
```

**输出结果**:
```
==================================================
SoftProxy 许可证生成器
==================================================
正在生成许可证...
到期日期: 2026-06-30
用户: VIP用户

✅ 许可证生成成功!
文件: license.dat
用户: VIP用户
到期日期: 2026-06-30
序列号: AE28C63E12CA2ED4
创建时间: 2025-07-23 15:14:58

📋 加密许可证内容:
==================================================
eyJhcHAiOiJTb2Z0UHJveHkiLCJleHBpcmVfZGF0ZSI6IjIwMjYtMDYtMzAiLCJ1c2VyIjoiVklQ
用户IiwiY3JlYXRlZCI6IjIwMjUtMDctMjMgMTU6MTQ6NTgiLCJzZXJpYWwiOiJBRTI4QzYzRTEy
Q0EyRUQ0In18NTgzOGY4M2NhMTZmM2IyN2NhNTNmYmMyYTM3NDk2YzU=
==================================================
```

## 📋 **许可证分发**

### **分发步骤**
1. **生成许可证**: 使用生成器创建许可证文件
2. **提供文件**: 将 `license.dat` 文件提供给用户
3. **使用说明**: 告知用户将文件放在软件根目录

### **用户使用方法**
1. 将收到的 `license.dat` 文件复制到软件根目录
2. 确保网络连接正常（用于时间验证）
3. 启动软件，系统会自动验证许可证

## 🔍 **验证机制**

### **验证流程**
1. **文件检查**: 检查 `license.dat` 文件是否存在
2. **解密验证**: Base64解码并验证数字签名
3. **网络时间**: 从多个时间服务器获取真实时间
4. **到期检查**: 比较网络时间与许可证到期日期
5. **结果处理**: 验证通过继续启动，失败显示错误并退出

### **网络时间服务器**
系统使用多个时间服务器确保可靠性：
- 苏宁时间API (国内)
- 淘宝时间API (国内)
- WorldTimeAPI (国外)
- TimeAPI.io (备用)

### **验证结果**
- ✅ **成功**: `许可证验证成功，剩余 X 天`
- ❌ **失败**: 显示具体错误信息并退出程序

## 🚨 **错误处理**

### **常见错误及解决方案**

#### **1. 许可证文件不存在**
```
错误: 许可证文件不存在
解决: 确保 license.dat 文件在软件根目录
```

#### **2. 许可证格式错误**
```
错误: 许可证格式错误
解决: 重新生成正确的许可证文件
```

#### **3. 许可证已过期**
```
错误: 许可证已过期 X 天（到期日期：YYYY-MM-DD）
解决: 生成新的许可证或延长到期日期
```

#### **4. 网络连接失败**
```
错误: 无法获取网络时间，请检查网络连接
解决: 确保网络连接正常，防火墙允许访问
```

#### **5. 签名验证失败**
```
错误: 许可证签名验证失败
解决: 许可证可能被篡改，重新生成许可证
```

## 🔧 **高级功能**

### **测试许可证**
```bash
# 测试当前许可证是否有效
python license_system.py
```

### **查看许可证信息**
运行验证程序会显示详细信息：
- 到期日期
- 剩余天数
- 用户名称
- 创建时间
- 序列号

### **批量生成许可证**
```bash
# 生成多个不同到期日期的许可证
python license_generator.py --expire-date 2025-12-31 --user "用户A" --output "license_A.dat"
python license_generator.py --expire-date 2026-06-30 --user "用户B" --output "license_B.dat"
```

## 🛡️ **安全特性**

### **加密保护**
- **Base64编码**: 防止直接查看许可证内容
- **数字签名**: SHA256签名防止篡改
- **应用绑定**: 许可证绑定特定应用
- **序列号**: 每个许可证都有唯一序列号

### **时间验证**
- **网络时间**: 强制使用网络时间，防止本地时间篡改
- **多服务器**: 使用多个时间服务器确保可靠性
- **容错机制**: 自动切换到可用的时间服务器

## 📝 **注意事项**

### **重要提醒**
1. **密钥保护**: 不要泄露 `license_generator.py` 中的密钥
2. **网络依赖**: 软件启动需要网络连接进行时间验证
3. **文件位置**: 许可证文件必须在软件根目录
4. **日期格式**: 使用 `YYYY-MM-DD` 格式指定到期日期

### **最佳实践**
1. **定期更新**: 建议定期更新许可证到期日期
2. **备份文件**: 保留许可证生成记录
3. **用户支持**: 为用户提供清晰的使用说明
4. **错误处理**: 提供友好的错误提示信息

---

**🎉 完整的加密许可证保护系统，安全可靠！**
