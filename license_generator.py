#!/usr/bin/env python3
"""
SoftProxy 许可证生成器
用于生成加密的许可证文件
"""

import json
import hashlib
import base64
from datetime import datetime
import argparse
import secrets

class LicenseGenerator:
    def __init__(self):
        self.secret_key = "SoftProxy2025SecretKey_DoNotShare"
        self.app_signature = "SOFTPROXY_V1"
    
    def _create_signature(self, data):
        """创建数据签名"""
        combined = f"{data}{self.secret_key}"
        return hashlib.sha256(combined.encode()).hexdigest()[:32]
    
    def generate_license(self, expire_date, user="Default User"):
        """生成加密许可证"""
        try:
            # 验证日期格式
            datetime.strptime(expire_date, '%Y-%m-%d')
            
            # 创建许可证数据
            license_data = {
                'app': 'SoftProxy',
                'expire_date': expire_date,
                'signature': self.app_signature,
                'user': user,
                'created': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'serial': secrets.token_hex(8).upper()
            }
            
            # 序列化数据
            data_str = json.dumps(license_data, separators=(',', ':'))
            
            # 创建签名
            signature = self._create_signature(data_str)
            
            # 组合数据和签名
            full_data = f"{data_str}|{signature}"
            
            # Base64编码
            encrypted_license = base64.b64encode(full_data.encode()).decode()
            
            return encrypted_license, license_data
            
        except ValueError:
            return None, "日期格式错误，请使用 YYYY-MM-DD 格式"
        except Exception as e:
            return None, f"生成许可证失败: {e}"
    
    def save_license(self, encrypted_license, filename="license.dat"):
        """保存许可证到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(encrypted_license)
            return True, f"许可证已保存到 {filename}"
        except Exception as e:
            return False, f"保存许可证失败: {e}"
    
    def verify_license(self, encrypted_license):
        """验证生成的许可证"""
        try:
            # Base64解码
            decoded = base64.b64decode(encrypted_license.encode()).decode()
            
            # 分离数据和签名
            if '|' not in decoded:
                return False, "许可证格式错误"
            
            data_str, signature = decoded.rsplit('|', 1)
            
            # 验证签名
            expected_signature = self._create_signature(data_str)
            if signature != expected_signature:
                return False, "许可证签名验证失败"
            
            # 解析数据
            license_data = json.loads(data_str)
            
            return True, license_data
            
        except Exception as e:
            return False, f"许可证验证错误: {e}"

def main():
    parser = argparse.ArgumentParser(description='SoftProxy 许可证生成器')
    parser.add_argument('--expire-date', '-e', type=str, required=True,
                       help='到期日期 (YYYY-MM-DD)')
    parser.add_argument('--user', '-u', type=str, default='Default User',
                       help='用户名称')
    parser.add_argument('--output', '-o', type=str, default='license.dat',
                       help='输出文件名')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("SoftProxy 许可证生成器")
    print("=" * 50)
    
    generator = LicenseGenerator()
    
    # 生成许可证
    print(f"正在生成许可证...")
    print(f"到期日期: {args.expire_date}")
    print(f"用户: {args.user}")
    
    encrypted_license, license_data = generator.generate_license(args.expire_date, args.user)
    
    if encrypted_license is None:
        print(f"❌ 生成失败: {license_data}")
        return
    
    # 验证生成的许可证
    is_valid, verify_result = generator.verify_license(encrypted_license)
    if not is_valid:
        print(f"❌ 许可证验证失败: {verify_result}")
        return
    
    # 保存许可证
    success, save_msg = generator.save_license(encrypted_license, args.output)
    
    if success:
        print(f"\n✅ 许可证生成成功!")
        print(f"文件: {args.output}")
        print(f"用户: {license_data['user']}")
        print(f"到期日期: {license_data['expire_date']}")
        print(f"序列号: {license_data['serial']}")
        print(f"创建时间: {license_data['created']}")
        
        print(f"\n📋 加密许可证内容:")
        print("=" * 50)
        print(encrypted_license)
        print("=" * 50)
        
        print(f"\n📝 使用说明:")
        print(f"1. 将 {args.output} 文件复制到软件根目录")
        print(f"2. 启动软件时会自动验证许可证")
        print(f"3. 需要网络连接进行时间验证")
        
    else:
        print(f"❌ 保存失败: {save_msg}")

if __name__ == "__main__":
    main()
