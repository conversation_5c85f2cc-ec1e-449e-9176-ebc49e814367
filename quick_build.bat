@echo off
echo Quick Build - SoftProxy
echo =======================

REM Clean and compile in one line
if exist "proxy_ui.dist" rmdir /s /q "proxy_ui.dist"
if exist "dist" rmdir /s /q "dist"

nuitka --standalone --enable-plugin=tk-inter --windows-console-mode=disable --include-data-dir=tools=tools --output-dir=dist --remove-output proxy_ui.py

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

ren "dist\proxy_ui.dist" "SoftProxy"
copy "智赞助手.exe" "dist\SoftProxy\" >nul 2>&1

echo Build complete: dist\SoftProxy\proxy_ui.exe
pause
