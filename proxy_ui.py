#!/usr/bin/env python3
"""
SoftProxy UI界面 - 简洁实用的代理控制界面
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
import subprocess
import time
import os
import psutil
import requests
import json
from datetime import datetime
import sys
from license_system import validate_license

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，支持打包后的环境"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后的临时目录
        return os.path.join(sys._MEIPASS, relative_path)
    elif hasattr(sys, 'frozen') and hasattr(sys, 'executable'):
        # Nuitka打包后的环境
        return os.path.join(os.path.dirname(sys.executable), relative_path)
    else:
        # 开发环境 - 使用脚本所在目录或当前工作目录
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            return os.path.join(script_dir, relative_path)
        except NameError:
            # 如果__file__不存在，使用当前工作目录
            return os.path.join(os.getcwd(), relative_path)

def get_data_path(filename):
    """获取数据文件路径，优先使用用户目录"""
    if hasattr(sys, 'frozen'):
        # 打包后环境，使用exe所在目录
        return os.path.join(os.path.dirname(sys.executable), filename)
    else:
        # 开发环境，使用脚本所在目录或当前工作目录
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            return os.path.join(script_dir, filename)
        except NameError:
            # 如果__file__不存在，使用当前工作目录
            return os.path.join(os.getcwd(), filename)

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class ProxyUI:
    def __init__(self):
        # 许可证验证
        if not self.validate_license():
            sys.exit(1)

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("SoftProxy")
        self.root.geometry("1000x600")
        self.root.minsize(800, 500)
        
        # 状态变量
        self.software_process = None
        self.proxy_server = None
        self.proxy_thread = None
        self.injection_process = None
        self.is_proxy_running = False
        self.is_injected = False

        # 统计数据文件路径
        self.stats_file = get_data_path("daily_stats.json")

        # 平台统计计数器 - 当日数据
        self.platform_stats = {
            'clover': {'requests': 0, 'success': 0},
            'panda': {'requests': 0, 'success': 0},
            'sihai': {'requests': 0, 'success': 0}
        }

        # 静默模式标志 - 用于一键启动时减少日志输出
        self.silent_mode = False

        # 软件路径 - 支持打包后环境
        self.software_path = get_data_path("智赞助手.exe")

        # 创建UI组件
        self.create_widgets()

        # 加载今日统计数据（在UI创建后）
        self.load_today_stats()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建所有UI组件"""
        # 主框架 - 使用水平布局
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 左侧Tab控制面板
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)

        # 创建Tab视图
        self.tabview = ctk.CTkTabview(left_frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)

        # 添加Tab页面
        self.tabview.add("首页")
        self.tabview.add("账号管理")
        self.tabview.add("平台设置")

        # 右侧日志面板
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)

        # 获取首页Tab
        proxy_tab = self.tabview.tab("首页")





        # 一键启动按钮（大按钮）
        self.one_click_button = ctk.CTkButton(
            proxy_tab,
            text="一键启动",
            command=self.one_click_start,
            width=260,
            height=45,
            font=ctk.CTkFont(size=15, weight="bold"),
            fg_color=("#1f538d", "#14375e"),
            hover_color=("#14375e", "#1f538d")
        )
        self.one_click_button.pack(pady=(0, 20))

        # 平台统计区域
        stats_frame = ctk.CTkFrame(proxy_tab)
        stats_frame.pack(fill="both", expand=True, padx=10, pady=(0, 15))

        # 统计标题和历史记录按钮
        title_frame = ctk.CTkFrame(stats_frame)
        title_frame.pack(fill="x", padx=15, pady=(15, 10))

        stats_title = ctk.CTkLabel(
            title_frame,
            text="今日统计",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(side="left", padx=10, pady=10)

        # 历史记录按钮
        history_button = ctk.CTkButton(
            title_frame,
            text="历史记录",
            width=80,
            height=28,
            font=ctk.CTkFont(size=12),
            command=self.show_history_stats
        )
        history_button.pack(side="right", padx=10, pady=10)

        # 各平台统计区域
        platforms_stats_frame = ctk.CTkFrame(stats_frame)
        platforms_stats_frame.pack(fill="x", padx=15, pady=10)

        # 创建3个平台的统计显示
        self.create_platform_stats(platforms_stats_frame)

        # 总计统计区域
        total_stats_frame = ctk.CTkFrame(stats_frame)
        total_stats_frame.pack(fill="x", padx=15, pady=(10, 15))

        # 创建总计统计显示
        self.create_total_stats(total_stats_frame)

        # 分步操作区域 (隐藏但保留功能)
        steps_frame = ctk.CTkFrame(proxy_tab)
        # steps_frame.pack(fill="x", padx=10, pady=(0, 15))  # 隐藏显示
        
        # 步骤标题
        steps_title = ctk.CTkLabel(
            steps_frame,
            text="📋 分步操作",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        steps_title.pack(pady=(15, 10))
        
        # 步骤1：打开点赞软件
        step1_frame = ctk.CTkFrame(steps_frame)
        step1_frame.pack(fill="x", padx=15, pady=5)
        
        ctk.CTkLabel(step1_frame, text="1. 打开点赞软件", font=ctk.CTkFont(size=14)).pack(side="left", padx=10, pady=10)
        self.open_software_button = ctk.CTkButton(
            step1_frame,
            text="打开软件",
            command=self.open_software,
            width=100,
            height=32
        )
        self.open_software_button.pack(side="right", padx=10, pady=10)
        
        # 步骤2：启动代理
        step2_frame = ctk.CTkFrame(steps_frame)
        step2_frame.pack(fill="x", padx=15, pady=5)
        
        ctk.CTkLabel(step2_frame, text="2. 启动代理服务器", font=ctk.CTkFont(size=14)).pack(side="left", padx=10, pady=10)
        self.start_proxy_button = ctk.CTkButton(
            step2_frame,
            text="启动代理",
            command=self.start_proxy,
            width=100,
            height=32
        )
        self.start_proxy_button.pack(side="right", padx=10, pady=10)
        
        # 步骤3：开始代理（注入）
        step3_frame = ctk.CTkFrame(steps_frame)
        step3_frame.pack(fill="x", padx=15, pady=(5, 15))

        # 代理地址配置
        proxy_config_frame = ctk.CTkFrame(step3_frame)
        proxy_config_frame.pack(fill="x", padx=10, pady=(10, 5))

        ctk.CTkLabel(proxy_config_frame, text="代理地址:", font=ctk.CTkFont(size=12)).pack(side="left", padx=5)
        self.proxy_address_entry = ctk.CTkEntry(
            proxy_config_frame,
            placeholder_text="127.0.0.1:1080",
            width=150
        )
        self.proxy_address_entry.pack(side="left", padx=5)
        self.proxy_address_entry.insert(0, "127.0.0.1:1080")

        # 注入按钮
        inject_frame = ctk.CTkFrame(step3_frame)
        inject_frame.pack(fill="x", padx=10, pady=(0, 10))

        ctk.CTkLabel(inject_frame, text="3. 开始代理（注入）", font=ctk.CTkFont(size=14)).pack(side="left", padx=10, pady=10)

        self.inject_button = ctk.CTkButton(
            inject_frame,
            text="开始代理",
            command=self.start_injection,
            width=100,
            height=32,
            state="disabled"
        )
        self.inject_button.pack(side="right", padx=10, pady=10)
        



        # 右侧日志区域
        ctk.CTkLabel(right_frame, text="实时日志", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(15, 10))

        # 日志文本框 - 占满右侧
        self.log_text = ctk.CTkTextbox(right_frame, wrap="word")
        self.log_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 初始化完成后创建Tab内容和加载数据
        self.root.after(100, lambda: self.add_log("系统启动", "SoftProxy UI界面已启动"))
        self.root.after(200, lambda: self.create_account_tab())
        self.root.after(300, lambda: self.create_load_balancer_tab())
        self.root.after(400, lambda: self.load_saved_accounts())

    def create_account_tab(self):
        """创建账号管理Tab"""
        account_tab = self.tabview.tab("账号管理")

        # 账号管理标题
        title_label = ctk.CTkLabel(
            account_tab,
            text="平台账号管理",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(10, 15))

        # 第一行：三叶草 + 熊猫
        row1_frame = ctk.CTkFrame(account_tab, fg_color="transparent")
        row1_frame.pack(fill="x", padx=10, pady=10)

        # 左列：三叶草平台
        left_frame1 = ctk.CTkFrame(row1_frame)
        left_frame1.pack(side="left", fill="both", expand=True, padx=(0, 5))
        self.create_platform_section(left_frame1, "三叶草", "clover")

        # 右列：熊猫平台
        right_frame1 = ctk.CTkFrame(row1_frame)
        right_frame1.pack(side="right", fill="both", expand=True, padx=(5, 0))
        self.create_platform_section(right_frame1, "熊猫", "panda")

        # 第二行：四海平台（居中显示）
        row2_frame = ctk.CTkFrame(account_tab, fg_color="transparent")
        row2_frame.pack(fill="x", padx=10, pady=(0, 10))

        # 四海平台（居中显示）
        sihai_frame = ctk.CTkFrame(row2_frame)
        sihai_frame.pack(padx=100, pady=5)  # 居中显示
        self.create_platform_section(sihai_frame, "四海", "sihai")

    def create_platform_section(self, parent, platform_name, platform_key):
        """创建单个平台的账号输入区域"""
        # 平台标题 - 直接在父容器中显示
        platform_label = ctk.CTkLabel(
            parent,
            text=f"{platform_name}平台",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        platform_label.pack(pady=(10, 8))

        # 账号密码输入区域 - 更紧凑的布局
        input_frame = ctk.CTkFrame(parent)
        input_frame.pack(fill="both", expand=True, padx=8, pady=(0, 10))

        # 账号输入
        account_frame = ctk.CTkFrame(input_frame)
        account_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(account_frame, text="账号:", font=ctk.CTkFont(size=11)).pack(side="left", padx=(8, 3))
        account_entry = ctk.CTkEntry(account_frame, placeholder_text="请输入账号", width=140, height=28)
        account_entry.pack(side="left", padx=3)

        # 密码输入
        password_frame = ctk.CTkFrame(input_frame)
        password_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(password_frame, text="密码:", font=ctk.CTkFont(size=11)).pack(side="left", padx=(8, 3))
        password_entry = ctk.CTkEntry(password_frame, placeholder_text="请输入密码", show="*", width=140, height=28)
        password_entry.pack(side="left", padx=3)

        # KEY显示和获取
        key_frame = ctk.CTkFrame(input_frame)
        key_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(key_frame, text="KEY:", font=ctk.CTkFont(size=11)).pack(side="left", padx=(8, 3))
        key_entry = ctk.CTkEntry(key_frame, placeholder_text="点击获取KEY", width=100, height=28, state="readonly")
        key_entry.pack(side="left", padx=3)

        get_key_button = ctk.CTkButton(
            key_frame,
            text="获取KEY",
            command=lambda: self.get_platform_key(platform_key),
            width=70,
            height=26,
            font=ctk.CTkFont(size=10)
        )
        get_key_button.pack(side="left", padx=3)

        # 保存输入框引用
        setattr(self, f"{platform_key}_account_entry", account_entry)
        setattr(self, f"{platform_key}_password_entry", password_entry)
        setattr(self, f"{platform_key}_key_entry", key_entry)

        # 绑定失去焦点事件，自动保存
        account_entry.bind("<FocusOut>", lambda e: self._auto_save_platform(platform_key))
        password_entry.bind("<FocusOut>", lambda e: self._auto_save_platform(platform_key))

    def get_platform_key(self, platform_key):
        """获取平台KEY"""
        try:
            # 获取对应平台的账号密码
            account_entry = getattr(self, f"{platform_key}_account_entry")
            password_entry = getattr(self, f"{platform_key}_password_entry")
            key_entry = getattr(self, f"{platform_key}_key_entry")

            account = account_entry.get().strip()
            password = password_entry.get().strip()

            if not account or not password:
                self.add_log("错误", f"{platform_key}平台账号或密码为空")
                return

            self.add_log("获取KEY", f"正在获取{platform_key}平台KEY...")

            # 在后台线程中获取KEY
            import threading
            thread = threading.Thread(
                target=self._fetch_platform_key,
                args=(platform_key, account, password, key_entry),
                daemon=True
            )
            thread.start()

        except Exception as e:
            self.add_log("异常", f"获取{platform_key}平台KEY异常: {e}")

    def _fetch_platform_key(self, platform_key, account, password, key_entry):
        """在后台获取平台KEY"""
        try:
            import requests

            # 根据平台选择不同的API端点和参数
            if platform_key == "clover":  # 三叶草
                api_url = "http://www.sanyecao.co:98/keys"
                method = "GET"
                params = {
                    "account": account,
                    "password": password
                }
                headers = {}
                data = None
            elif platform_key == "panda":  # 熊猫
                api_url = "http://**************:8020/apikey"
                method = "GET"
                params = {
                    "account": account,
                    "pwd": password
                }
                headers = {}
                data = None
            elif platform_key == "sihai":  # 四海
                api_url = "http://meetspace.top:2095/order/login"
                method = "POST"
                params = None
                headers = {
                    'Host': 'meetspace.top:2095',
                    'User-Agent': 'Go-http-client/1.1',
                    'Accept-Encoding': 'gzip',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
                data = {
                    "username": account,
                    "password": password
                }
            else:
                self.root.after(0, lambda: self.add_log("错误", f"未知平台: {platform_key}"))
                return

            self.root.after(0, lambda: self.add_log("请求", f"请求URL: {api_url}"))
            if method == "GET":
                self.root.after(0, lambda: self.add_log("参数", f"GET参数: account={account}, password=***"))
            else:
                self.root.after(0, lambda: self.add_log("参数", f"POST数据: username={account}, password=***"))

            # 发送请求
            if method == "GET":
                response = requests.get(api_url, params=params, timeout=10)
            else:  # POST
                response = requests.post(api_url, headers=headers, data=data, timeout=10)

            self.root.after(0, lambda: self.add_log("响应", f"状态码: {response.status_code}"))

            if response.status_code == 200:
                try:
                    # 解析JSON响应
                    result_json = response.json()
                    self.root.after(0, lambda: self.add_log("响应", f"JSON: {result_json}"))

                    # 处理不同平台的响应格式
                    access_key = ""
                    if platform_key == "panda":  # 熊猫平台格式
                        if result_json.get("success") and result_json.get("code") == 0:
                            access_key = result_json.get("data", {}).get("accesskey", "")
                    elif platform_key == "clover":  # 三叶草平台格式
                        if result_json.get("code") == 0:
                            access_key = result_json.get("data", {}).get("accesskey", "")
                    elif platform_key == "sihai":  # 四海平台格式
                        if result_json.get("code") == 1:  # 四海平台成功码是1
                            access_key = result_json.get("token", "")

                    if access_key:
                        self.root.after(0, lambda: self.add_log("成功", f"获取到{platform_key}平台KEY: {access_key}"))

                        # 更新KEY输入框
                        self.root.after(0, lambda: self._update_key_entry(key_entry, access_key))

                        # 保存账号信息 (获取KEY成功后自动保存)
                        self.root.after(0, lambda: self._save_platform_account(platform_key, account, password, access_key))
                    else:
                        error_msg = result_json.get("msg", "未知错误")
                        self.root.after(0, lambda: self.add_log("失败", f"API返回错误: {error_msg}"))

                except json.JSONDecodeError:
                    # 如果不是JSON，按原文本处理
                    result = response.text.strip()
                    self.root.after(0, lambda: self.add_log("成功", f"获取到KEY: {result}"))
                    self.root.after(0, lambda: self._update_key_entry(key_entry, result))
                    self.root.after(0, lambda: self._save_platform_account(platform_key, account, password, result))

            else:
                error_msg = f"获取KEY失败，状态码: {response.status_code}"
                if response.text:
                    error_msg += f", 响应: {response.text}"
                self.root.after(0, lambda: self.add_log("失败", error_msg))

        except requests.exceptions.Timeout:
            self.root.after(0, lambda: self.add_log("超时", f"获取{platform_key}平台KEY超时"))
        except requests.exceptions.RequestException as e:
            self.root.after(0, lambda: self.add_log("网络错误", f"请求失败: {e}"))
        except Exception as e:
            self.root.after(0, lambda: self.add_log("异常", f"获取KEY异常: {e}"))

    def _update_key_entry(self, key_entry, key_value):
        """更新KEY输入框"""
        try:
            key_entry.configure(state="normal")
            key_entry.delete(0, "end")
            key_entry.insert(0, key_value)
            key_entry.configure(state="readonly")
        except Exception as e:
            self.add_log("异常", f"更新KEY显示异常: {e}")

    def _save_platform_account(self, platform_key, account, password, key):
        """保存平台账号信息"""
        try:
            import json
            import os

            # 创建账号配置文件路径
            config_file = get_data_path("platform_accounts.json")

            # 读取现有配置
            accounts = {}
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        accounts = json.load(f)
                except:
                    accounts = {}

            # 更新配置
            accounts[platform_key] = {
                "account": account,
                "password": password,
                "key": key,
                "updated_time": str(datetime.now())
            }

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

            self.add_log("保存", f"{platform_key}平台账号信息已保存")

        except Exception as e:
            self.add_log("异常", f"保存账号信息异常: {e}")

    def _auto_save_platform(self, platform_key):
        """自动保存单个平台的账号信息（失去焦点时）"""
        try:
            account_entry = getattr(self, f"{platform_key}_account_entry")
            password_entry = getattr(self, f"{platform_key}_password_entry")
            key_entry = getattr(self, f"{platform_key}_key_entry")

            account = account_entry.get().strip()
            password = password_entry.get().strip()
            key = key_entry.get().strip()

            # 只有账号和密码都不为空时才保存
            if account and password:
                self._save_platform_account(platform_key, account, password, key)
                self.add_log("自动保存", f"{platform_key}平台账号信息已自动保存")

        except Exception as e:
            self.add_log("异常", f"自动保存{platform_key}平台信息异常: {e}")

    def save_accounts(self):
        """保存账号信息"""
        self.add_log("账号", "手动保存账号信息...")

        # 获取所有平台的信息并保存
        platforms = ["clover", "panda", "sihai"]
        for platform_key in platforms:
            try:
                account_entry = getattr(self, f"{platform_key}_account_entry")
                password_entry = getattr(self, f"{platform_key}_password_entry")
                key_entry = getattr(self, f"{platform_key}_key_entry")

                account = account_entry.get().strip()
                password = password_entry.get().strip()
                key = key_entry.get().strip()

                if account and password:
                    self._save_platform_account(platform_key, account, password, key)

            except Exception as e:
                self.add_log("异常", f"保存{platform_key}平台信息异常: {e}")

        self.add_log("完成", "账号信息保存完成")

    def load_saved_accounts(self):
        """加载已保存的账号信息"""
        try:
            config_file = get_data_path("platform_accounts.json")
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)

                # 加载各平台账号信息
                for platform_key in ["clover", "panda", "sihai"]:
                    if platform_key in accounts:
                        account_data = accounts[platform_key]

                        # 填充账号信息
                        account_entry = getattr(self, f"{platform_key}_account_entry")
                        password_entry = getattr(self, f"{platform_key}_password_entry")
                        key_entry = getattr(self, f"{platform_key}_key_entry")

                        account_entry.insert(0, account_data.get("account", ""))
                        password_entry.insert(0, account_data.get("password", ""))

                        if account_data.get("key"):
                            self._update_key_entry(key_entry, account_data.get("key"))

                self.add_log("加载", "已加载保存的账号信息")
            else:
                self.add_log("加载", "未找到保存的账号信息，请手动输入")

        except Exception as e:
            self.add_log("异常", f"加载账号信息异常: {e}")

    def create_load_balancer_tab(self):
        """创建平台设置Tab"""
        lb_tab = self.tabview.tab("平台设置")

        # 平台设置标题
        title_label = ctk.CTkLabel(
            lb_tab,
            text="⚙️ 平台设置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(5, 10))

        # 策略选择区域 - 更紧凑的设计
        strategy_frame = ctk.CTkFrame(lb_tab)
        strategy_frame.pack(fill="x", padx=10, pady=(0, 8))

        ctk.CTkLabel(strategy_frame, text="📋 策略选择", font=ctk.CTkFont(size=13, weight="bold")).pack(pady=(8, 3))

        # 策略单选按钮 - 更紧凑的布局
        self.strategy_var = ctk.StringVar(value="priority")

        strategy_radio_frame = ctk.CTkFrame(strategy_frame)
        strategy_radio_frame.pack(pady=(0, 8))

        self.priority_radio = ctk.CTkRadioButton(
            strategy_radio_frame,
            text="优先级轮询",
            variable=self.strategy_var,
            value="priority",
            command=self.on_strategy_change,
            font=ctk.CTkFont(size=12)
        )
        self.priority_radio.pack(side="left", padx=15, pady=5)

        self.ratio_radio = ctk.CTkRadioButton(
            strategy_radio_frame,
            text="按比例分配",
            variable=self.strategy_var,
            value="ratio",
            command=self.on_strategy_change,
            font=ctk.CTkFont(size=12)
        )
        self.ratio_radio.pack(side="left", padx=15, pady=5)

        # 平台启用配置
        self.create_platform_enable_config(lb_tab)

        # 优先级轮询配置
        self.create_priority_config(lb_tab)

        # 按比例分配配置
        self.create_ratio_config(lb_tab)

        # 加载保存的配置
        self.load_lb_config()

        # 初始化显示状态（不自动保存）
        self.on_strategy_change(auto_save=False)

    def load_lb_config(self):
        """加载负载均衡配置"""
        try:
            with open(get_data_path("load_balancer_config.json"), "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载策略设置
            strategy = config.get("strategy", "priority")
            self.strategy_var.set(strategy)

            # 加载主平台设置
            main_platform = config.get("main_platform", "熊猫")
            self.main_platform_var.set(main_platform)

            # 加载平台启用设置
            platform_enabled = config.get("platform_enabled", {})
            self.clover_enable_var.set(platform_enabled.get("clover", True))
            self.panda_enable_var.set(platform_enabled.get("panda", True))
            self.sihai_enable_var.set(platform_enabled.get("sihai", True))

            # 加载权重设置
            weights = config.get("weights", {})
            self.clover_weight.delete(0, "end")
            self.clover_weight.insert(0, str(weights.get("clover", 4)))

            self.panda_weight.delete(0, "end")
            self.panda_weight.insert(0, str(weights.get("panda", 3)))

            self.sihai_weight.delete(0, "end")
            self.sihai_weight.insert(0, str(weights.get("sihai", 1)))

            # 更新显示状态
            self.on_strategy_change()

            self.add_log("配置加载", "负载均衡配置已加载")

        except FileNotFoundError:
            self.add_log("配置加载", "未找到配置文件，使用默认配置")
        except Exception as e:
            self.add_log("配置加载", f"加载配置失败: {e}")

    def create_platform_enable_config(self, parent):
        """创建平台启用配置区域"""
        enable_frame = ctk.CTkFrame(parent)
        enable_frame.pack(fill="x", padx=10, pady=(0, 8))

        ctk.CTkLabel(enable_frame, text="🔧 平台启用设置", font=ctk.CTkFont(size=13, weight="bold")).pack(pady=(8, 5))

        # 平台启用复选框 - 水平布局
        checkbox_container = ctk.CTkFrame(enable_frame)
        checkbox_container.pack(pady=(0, 8))

        # 三叶草启用
        self.clover_enable_var = ctk.BooleanVar(value=True)
        self.clover_enable_checkbox = ctk.CTkCheckBox(
            checkbox_container,
            text="🍀 三叶草平台",
            variable=self.clover_enable_var,
            command=self.on_platform_enable_change,
            font=ctk.CTkFont(size=11)
        )
        self.clover_enable_checkbox.pack(side="left", padx=15, pady=5)

        # 熊猫启用
        self.panda_enable_var = ctk.BooleanVar(value=True)
        self.panda_enable_checkbox = ctk.CTkCheckBox(
            checkbox_container,
            text="🐼 熊猫平台",
            variable=self.panda_enable_var,
            command=self.on_platform_enable_change,
            font=ctk.CTkFont(size=11)
        )
        self.panda_enable_checkbox.pack(side="left", padx=15, pady=5)

        # 四海启用
        self.sihai_enable_var = ctk.BooleanVar(value=True)
        self.sihai_enable_checkbox = ctk.CTkCheckBox(
            checkbox_container,
            text="🌊 四海平台",
            variable=self.sihai_enable_var,
            command=self.on_platform_enable_change,
            font=ctk.CTkFont(size=11)
        )
        self.sihai_enable_checkbox.pack(side="left", padx=15, pady=5)

        # 说明文字
        info_label = ctk.CTkLabel(
            enable_frame,
            text="💡 只有启用的平台才会参与负载均衡，至少需要启用一个平台",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        info_label.pack(pady=(0, 8))

    def create_priority_config(self, parent):
        """创建优先级轮询配置区域"""
        self.priority_frame = ctk.CTkFrame(parent)
        self.priority_frame.pack(fill="x", padx=10, pady=(0, 8))

        ctk.CTkLabel(self.priority_frame, text="📊 优先级轮询配置", font=ctk.CTkFont(size=13, weight="bold")).pack(pady=(8, 3))

        # 主平台选择 - 更清晰的布局
        main_platform_frame = ctk.CTkFrame(self.priority_frame)
        main_platform_frame.pack(pady=(0, 8))

        ctk.CTkLabel(main_platform_frame, text="主平台:", font=ctk.CTkFont(size=12)).pack(side="left", padx=(10, 5))

        self.main_platform_var = ctk.StringVar(value="熊猫")
        self.main_platform_combo = ctk.CTkComboBox(
            main_platform_frame,
            values=["三叶草", "熊猫", "四海"],
            variable=self.main_platform_var,
            state="readonly",
            width=100,
            height=28,
            font=ctk.CTkFont(size=11),
            command=lambda value: self.auto_save_lb_config()
        )
        self.main_platform_combo.pack(side="left", padx=5)

        # 说明文字 - 更小的字体
        info_label = ctk.CTkLabel(
            self.priority_frame,
            text="💡 优先获取主平台任务，无任务时自动轮询其他平台",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        info_label.pack(pady=(0, 8))

    def create_ratio_config(self, parent):
        """创建按比例分配配置区域"""
        self.ratio_frame = ctk.CTkFrame(parent)
        self.ratio_frame.pack(fill="x", padx=10, pady=(0, 8))

        ctk.CTkLabel(self.ratio_frame, text="⚖️ 按比例分配配置", font=ctk.CTkFont(size=13, weight="bold")).pack(pady=(8, 5))

        # 权重设置区域 - 水平布局，更紧凑
        weights_container = ctk.CTkFrame(self.ratio_frame)
        weights_container.pack(pady=(0, 8))

        # 三叶草权重
        clover_frame = ctk.CTkFrame(weights_container)
        clover_frame.pack(side="left", padx=8, pady=5)

        ctk.CTkLabel(clover_frame, text="🍀 三叶草权重:", font=ctk.CTkFont(size=11)).pack(pady=(3, 1))
        self.clover_weight = ctk.CTkEntry(clover_frame, width=70, height=30, justify="center", font=ctk.CTkFont(size=12))
        self.clover_weight.pack(pady=(0, 3))
        self.clover_weight.insert(0, "4")
        self.clover_weight.bind("<FocusOut>", lambda e: self.auto_save_lb_config())

        # 熊猫权重
        panda_frame = ctk.CTkFrame(weights_container)
        panda_frame.pack(side="left", padx=8, pady=5)

        ctk.CTkLabel(panda_frame, text="🐼 熊猫权重:", font=ctk.CTkFont(size=11)).pack(pady=(3, 1))
        self.panda_weight = ctk.CTkEntry(panda_frame, width=70, height=30, justify="center", font=ctk.CTkFont(size=12))
        self.panda_weight.pack(pady=(0, 3))
        self.panda_weight.insert(0, "3")
        self.panda_weight.bind("<FocusOut>", lambda e: self.auto_save_lb_config())

        # 四海权重
        sihai_frame = ctk.CTkFrame(weights_container)
        sihai_frame.pack(side="left", padx=8, pady=5)

        ctk.CTkLabel(sihai_frame, text="🌊 四海权重:", font=ctk.CTkFont(size=11)).pack(pady=(3, 1))
        self.sihai_weight = ctk.CTkEntry(sihai_frame, width=70, height=30, justify="center", font=ctk.CTkFont(size=12))
        self.sihai_weight.pack(pady=(0, 3))
        self.sihai_weight.insert(0, "1")
        self.sihai_weight.bind("<FocusOut>", lambda e: self.auto_save_lb_config())



        # 说明文字 - 更小的字体
        info_label = ctk.CTkLabel(
            self.ratio_frame,
            text="💡 按权重比例轮询分配，如 4:3:1 = 三叶草4次，熊猫3次，四海1次",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        info_label.pack(pady=(0, 8))



    def on_strategy_change(self, auto_save=True):
        """策略选择改变时的处理"""
        strategy = self.strategy_var.get()

        if strategy == "priority":
            # 显示优先级配置，隐藏比例配置
            self.priority_frame.pack(fill="x", padx=10, pady=(0, 15))
            self.ratio_frame.pack_forget()
            strategy_name = "优先级轮询"
        else:
            # 显示比例配置，隐藏优先级配置
            self.ratio_frame.pack(fill="x", padx=10, pady=(0, 15))
            self.priority_frame.pack_forget()
            strategy_name = "按比例分配"

        self.add_log("策略", f"负载均衡策略切换为: {strategy_name}")

        # 自动保存配置（可选）
        if auto_save:
            self.auto_save_lb_config()



    def on_platform_enable_change(self):
        """平台启用状态改变时的处理"""
        enabled_platforms = []
        if self.clover_enable_var.get():
            enabled_platforms.append("三叶草")
        if self.panda_enable_var.get():
            enabled_platforms.append("熊猫")
        if self.sihai_enable_var.get():
            enabled_platforms.append("四海")

        if not enabled_platforms:
            # 如果没有启用任何平台，强制启用熊猫平台
            self.panda_enable_var.set(True)
            enabled_platforms = ["熊猫"]
            self.add_log("警告", "至少需要启用一个平台，已自动启用熊猫平台")

        enabled_text = "、".join(enabled_platforms)
        self.add_log("平台设置", f"已启用平台: {enabled_text}")

        # 自动保存配置
        self.auto_save_lb_config()

    def auto_save_lb_config(self):
        """自动保存负载均衡配置"""
        try:
            config = {
                "strategy": self.strategy_var.get(),
                "main_platform": self.main_platform_var.get(),
                "platform_enabled": {
                    "clover": self.clover_enable_var.get(),
                    "panda": self.panda_enable_var.get(),
                    "sihai": self.sihai_enable_var.get()
                },
                "weights": {
                    "clover": int(self.clover_weight.get() or "0"),
                    "panda": int(self.panda_weight.get() or "0"),
                    "sihai": int(self.sihai_weight.get() or "0")
                }
            }

            # 保存到文件
            with open(get_data_path("load_balancer_config.json"), "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.add_log("自动保存", "平台设置配置已自动保存")

        except Exception as e:
            self.add_log("异常", f"自动保存配置异常: {e}")



    def add_log(self, category, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{category}] {message}\n"
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
    
    def check_software_running(self):
        """检查软件是否在运行"""
        found_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name']
                if proc_name and '智赞助手.exe' in proc_name:
                    found_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc_name,
                        'exe': proc.info['exe']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if found_processes:
            # 如果找到多个进程，返回第一个
            proc = found_processes[0]
            if not self.silent_mode:
                self.add_log("发现", f"找到目标进程: {proc['name']} (PID: {proc['pid']})")
                if len(found_processes) > 1:
                    self.add_log("警告", f"发现多个相关进程，共{len(found_processes)}个")
            return proc['pid']

        return None
    
    def open_software(self):
        """打开点赞软件"""
        try:
            if not os.path.exists(self.software_path):
                self.add_log("错误", f"软件路径不存在: {self.software_path}")
                messagebox.showerror("错误", f"找不到软件文件:\n{self.software_path}")
                return
            
            # 检查是否已经在运行
            pid = self.check_software_running()
            if pid:
                if not self.silent_mode:
                    self.add_log("信息", f"软件已在运行 (PID: {pid})")
                return

            if not self.silent_mode:
                self.add_log("操作", "正在启动点赞软件...")

            # 启动软件
            self.software_process = subprocess.Popen([self.software_path])

            # 等待软件启动
            time.sleep(2)

            # 检查是否成功启动
            pid = self.check_software_running()
            if pid:
                if not self.silent_mode:
                    self.add_log("成功", f"软件启动成功 (PID: {pid})")
            else:
                if not self.silent_mode:
                    self.add_log("警告", "软件可能启动失败，请手动检查")
                
        except Exception as e:
            self.add_log("错误", f"启动软件失败: {e}")
            messagebox.showerror("错误", f"启动软件失败:\n{e}")
    
    def start_proxy(self):
        """启动代理服务器"""
        if self.is_proxy_running:
            if not self.silent_mode:
                self.add_log("信息", "代理服务器已在运行")
            return

        try:
            if not self.silent_mode:
                self.add_log("操作", "正在启动代理服务器...")

            # 创建SOCKS5负载均衡代理服务器实例
            from socks5_load_balancer import SOCKS5LoadBalancer
            self.proxy_server = SOCKS5LoadBalancer(host='127.0.0.1', port=1080)
            # 设置UI实例引用，用于统计更新
            self.proxy_server.load_balancer.ui_instance = self

            # 在后台线程中启动代理服务器
            self.proxy_thread = threading.Thread(target=self._run_proxy_server)
            self.proxy_thread.daemon = True
            self.proxy_thread.start()

            # 等待一下让服务器启动
            time.sleep(1)

            self.is_proxy_running = True
            if not self.silent_mode:
                self.add_log("成功", "SOCKS5负载均衡代理启动成功 (127.0.0.1:1080)")
            self.inject_button.configure(state="normal")
            
        except Exception as e:
            self.add_log("错误", f"启动代理服务器失败: {e}")
            messagebox.showerror("错误", f"启动代理服务器失败:\n{e}")
    
    def _run_proxy_server(self):
        """在后台运行SOCKS5负载均衡代理服务器"""
        try:
            self.proxy_server.start()
        except Exception as e:
            self.add_log("错误", f"SOCKS5负载均衡代理运行时出错: {e}")
    
    def start_injection(self):
        """开始注入代理"""
        if self.is_injected:
            if not self.silent_mode:
                self.add_log("信息", "已经代理过了")
            return

        # 检查软件是否在运行
        pid = self.check_software_running()
        if not pid:
            if not self.silent_mode:
                self.add_log("错误", "软件未运行，请先启动软件")
                messagebox.showerror("错误", "请先启动点赞软件")
            return

        # 检查代理是否在运行
        if not self.is_proxy_running:
            if not self.silent_mode:
                self.add_log("错误", "代理服务器未运行，请先启动代理")
                messagebox.showerror("错误", "请先启动代理服务器")
            return

        # 禁用注入按钮，防止重复点击
        self.inject_button.configure(state="disabled", text="注入中...")

        # 直接在主线程中执行PowerShell注入，但使用非阻塞方式
        self._powershell_injection_direct(pid)

    def _simple_injection(self, pid):
        """使用最简单可靠的方式执行注入"""
        try:
            proxy_address = self.proxy_address_entry.get().strip()
            if not proxy_address:
                proxy_address = "127.0.0.1:1080"

            self.add_log("代理", f"开始代理 PID: {pid} -> {proxy_address}")

            # 使用最简单的命令调用方式，直接执行
            cmd = [get_resource_path("tools/proxinjector-cli.exe"), "-i", str(pid), "-p", proxy_address, "-l"]
            self.add_log("命令", f"执行: {' '.join(cmd)}")

            # 使用run方式，但在后台线程中执行
            threading.Thread(
                target=self._execute_injection_command,
                args=(cmd, pid),
                daemon=True
            ).start()

        except Exception as e:
            self.add_log("异常", f"启动代理失败: {e}")
            self.inject_button.configure(state="normal", text="开始代理")

    def _execute_injection_command(self, cmd, pid):
        """在后台线程中执行注入命令 - 添加详细环境检查"""
        try:
            # 详细的环境检查
            current_dir = os.getcwd()
            self.root.after(0, lambda: self.add_log("环境", f"当前工作目录: {current_dir}"))

            # 检查可执行文件
            exe_path = get_resource_path("tools/proxinjector-cli.exe")
            if os.path.exists(exe_path):
                self.root.after(0, lambda: self.add_log("环境", f"找到可执行文件: {exe_path}"))
            else:
                self.root.after(0, lambda: self.add_log("错误", f"找不到可执行文件: {exe_path}"))
                return

            # 检查DLL文件
            dll_files = ["proxinjectee.dll", "proxinjectee32.dll"]
            for dll in dll_files:
                dll_path = get_resource_path(f"tools/{dll}")
                if os.path.exists(dll_path):
                    self.root.after(0, lambda d=dll: self.add_log("环境", f"找到DLL: {d}"))
                else:
                    self.root.after(0, lambda d=dll: self.add_log("警告", f"缺少DLL: {d}"))

            # 检查目标进程是否存在
            try:
                proc = psutil.Process(pid)
                self.root.after(0, lambda: self.add_log("环境", f"目标进程: {proc.name()} ({proc.exe()})"))
            except:
                self.root.after(0, lambda: self.add_log("错误", f"目标进程 {pid} 不存在"))
                return

            # 使用绝对路径执行命令
            abs_cmd = [exe_path] + cmd[1:]  # 替换第一个元素为绝对路径
            self.root.after(0, lambda: self.add_log("命令", f"使用绝对路径: {' '.join(abs_cmd)}"))

            # 使用Popen进行非阻塞调用，添加更多参数来避免阻塞
            process = subprocess.Popen(
                abs_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,  # 添加stdin管道
                text=True,
                cwd=current_dir,
                env=os.environ.copy(),  # 确保环境变量完整
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0  # Windows下创建新进程组
            )

            self.root.after(0, lambda: self.add_log("进程", f"启动了subprocess，PID: {process.pid}"))

            # 定期检查进程状态，而不是阻塞等待
            self._monitor_injection_process(process, pid, 0)

        except Exception as e:
            self.root.after(0, lambda: self._update_injection_error(str(e), pid))

    def _monitor_injection_process(self, process, pid, elapsed_time):
        """定期监控注入进程状态"""
        # 检查进程是否完成
        poll_result = process.poll()

        if poll_result is not None:
            # 进程已完成，获取输出
            try:
                stdout, stderr = process.communicate(timeout=1)
                self.root.after(0, lambda: self._update_injection_result_from_popen(poll_result, stdout, stderr, pid))
            except:
                self.root.after(0, lambda: self._update_injection_error("获取输出失败", pid))
        elif elapsed_time >= 90:  # 增加到90秒
            # 超时，杀死进程
            try:
                process.kill()
                self.root.after(0, lambda: self._update_injection_timeout(pid))
            except:
                pass
        else:
            # 继续监控，每秒检查一次
            elapsed_time += 1
            if elapsed_time % 5 == 0:  # 每5秒更新一次进度
                self.root.after(0, lambda: self.add_log("进度", f"代理进行中... ({elapsed_time}秒)"))

            # 1秒后再次检查
            self.root.after(1000, lambda: self._monitor_injection_process(process, pid, elapsed_time))

    def _update_injection_result_from_popen(self, returncode, stdout, stderr, pid):
        """处理Popen的注入结果"""
        self.add_log("结果", f"返回码: {returncode}")

        if stdout:
            for line in stdout.strip().split('\n'):
                if line.strip():
                    self.add_log("输出", line.strip())

        if stderr:
            for line in stderr.strip().split('\n'):
                if line.strip():
                    self.add_log("错误", line.strip())

        if returncode == 0:
            self.add_log("成功", "代理成功!")
            self.is_injected = True
        else:
            self.add_log("失败", f"代理失败，返回码: {returncode}")
            self.inject_button.configure(state="normal", text="开始代理")

    def _update_injection_result(self, result, pid):
        """在主线程中更新注入结果"""
        self.add_log("结果", f"返回码: {result.returncode}")

        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    self.add_log("输出", line.strip())

        if result.stderr:
            for line in result.stderr.strip().split('\n'):
                if line.strip():
                    self.add_log("错误", line.strip())

        if result.returncode == 0:
            self.add_log("成功", "代理成功!")
            self.injection_status.configure(text="🟢注入")
            self.is_injected = True
        else:
            self.add_log("失败", f"代理失败，返回码: {result.returncode}")
            self.inject_button.configure(state="normal", text="开始代理")

    def _update_injection_timeout(self, pid):
        """处理注入超时"""
        self.add_log("超时", "代理命令超时（60秒）")
        self.inject_button.configure(state="normal", text="开始代理")

    def _update_injection_error(self, error, pid):
        """处理注入错误"""
        self.add_log("异常", f"代理异常: {error}")
        self.inject_button.configure(state="normal", text="开始代理")





    def _perform_injection(self, pid):
        """在后台线程中执行注入"""
        try:
            proxy_address = self.proxy_address_entry.get().strip()
            if not proxy_address:
                proxy_address = "127.0.0.1:1080"

            self.add_log("开始", f"=== 代理进程 {pid} ===")
            self.add_log("配置", f"代理地址: {proxy_address}")

            # 详细的环境检查
            self.add_log("环境", f"当前工作目录: {os.getcwd()}")
            self.add_log("环境", f"Python进程PID: {os.getpid()}")

            # 检查文件是否存在
            cli_path = get_resource_path("tools/proxinjector-cli.exe")
            if not os.path.exists(cli_path):
                self.add_log("错误", f"找不到 {cli_path}")
                return

            self.add_log("检查", f"找到代理工具: {cli_path}")

            # 检查DLL文件
            dll_files = ["proxinjectee.dll", "proxinjectee32.dll"]
            for dll in dll_files:
                dll_path = get_resource_path(f"tools/{dll}")
                if os.path.exists(dll_path):
                    self.add_log("检查", f"找到DLL: {dll}")
                else:
                    self.add_log("警告", f"缺少DLL: {dll}")

            # 再次确认目标进程
            current_pid = self.check_software_running()
            if not current_pid:
                self.add_log("错误", "目标进程已不存在")
                return
            elif current_pid != pid:
                self.add_log("更新", f"进程PID已变化: {pid} -> {current_pid}")
                pid = current_pid

            self.add_log("确认", f"目标进程存在: PID {pid}")

            # 检查进程详细信息
            try:
                proc = psutil.Process(pid)
                self.add_log("进程", f"进程名: {proc.name()}")
                self.add_log("进程", f"进程路径: {proc.exe()}")
                self.add_log("进程", f"进程状态: {proc.status()}")
            except Exception as e:
                self.add_log("警告", f"无法获取进程详细信息: {e}")

            # 构建代理命令
            cmd = [cli_path, "-i", str(pid), "-p", proxy_address, "-l"]
            self.add_log("命令", f"执行: {' '.join(cmd)}")

            # 记录开始时间
            start_time = time.time()
            self.add_log("执行", "开始执行代理命令...")

            # 执行代理命令 - 简化参数，模拟命令行调用
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            # 记录执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            self.add_log("时间", f"命令执行耗时: {execution_time:.2f}秒")

            # 详细的结果分析
            self.add_log("结果", f"返回码: {result.returncode}")

            if result.stdout:
                stdout_lines = result.stdout.strip().split('\n')
                for i, line in enumerate(stdout_lines):
                    self.add_log("输出", f"[{i+1}] {line}")
            else:
                self.add_log("输出", "无标准输出")

            if result.stderr:
                stderr_lines = result.stderr.strip().split('\n')
                for i, line in enumerate(stderr_lines):
                    self.add_log("错误", f"[{i+1}] {line}")
            else:
                self.add_log("错误", "无错误输出")

            # 分析结果
            if result.returncode == 0:
                self.add_log("成功", f"进程代理成功! PID: {pid}")
                self.injection_status.configure(text="🟢注入")
                self.is_injected = True

                # 验证代理状态
                self.add_log("验证", "正在验证代理状态...")
                time.sleep(1)
                self.add_log("验证", "代理验证完成")

            else:
                self.add_log("失败", f"代理失败，返回码: {result.returncode}")

                # 分析常见错误
                if result.returncode == 1:
                    self.add_log("分析", "可能原因: 权限不足、进程不存在或进程保护")
                elif result.returncode == -1:
                    self.add_log("分析", "可能原因: 程序崩溃或被安全软件阻止")
                elif result.returncode == 5:
                    self.add_log("分析", "可能原因: 拒绝访问，需要管理员权限")
                else:
                    self.add_log("分析", f"未知错误码: {result.returncode}")

                self.add_log("建议", "1. 确保以管理员身份运行")
                self.add_log("建议", "2. 检查杀毒软件设置")
                self.add_log("建议", "3. 尝试重启目标软件")

        except subprocess.TimeoutExpired:
            self.add_log("超时", "代理命令执行超时（120秒）")
            self.add_log("说明", "代理过程通常需要30-60秒，超时可能因为:")
            self.add_log("原因", "1. 权限不足，需要管理员权限")
            self.add_log("原因", "2. 杀毒软件阻止了代理操作")
            self.add_log("原因", "3. 目标进程有保护机制")
            self.add_log("原因", "4. 系统资源不足或网络问题")
        except FileNotFoundError:
            self.add_log("错误", "找不到 proxinjector-cli.exe 文件")
            self.add_log("解决", "请确保文件在当前目录下")
        except PermissionError:
            self.add_log("权限", "权限不足")
            self.add_log("解决", "请以管理员身份运行程序")
        except Exception as e:
            self.add_log("异常", f"代理过程异常: {type(e).__name__}")
            self.add_log("异常", f"异常详情: {str(e)}")
            self.add_log("解决", "请检查系统环境和权限设置")

        finally:
            self.add_log("完成", "=== 代理流程结束 ===")
            # 恢复按钮状态
            if not self.is_injected:
                self.inject_button.configure(state="normal", text="开始代理")
    
    def one_click_start(self):
        """一键启动所有功能 - 非阻塞版本"""
        self.add_log("一键启动", "开始一键启动")

        # 禁用一键启动按钮
        self.one_click_button.configure(state="disabled", text="启动中...")

        # 设置静默模式标志
        self.silent_mode = True

        # 步骤1：打开软件
        self.open_software()

        # 强制更新UI并继续下一步
        self.root.update()
        self.root.after(3000, self._one_click_step2)

    def _one_click_step2(self):
        """一键启动步骤2：启动代理"""
        self.start_proxy()

        # 强制更新UI并继续下一步
        self.root.update()
        self.root.after(2000, self._one_click_step3)

    def _one_click_step3(self):
        """一键启动步骤3：开始注入"""
        self.start_injection()

        # 完成流程
        self.root.after(1000, self._one_click_complete)

    def _one_click_complete(self):
        """一键启动完成"""
        # 取消静默模式
        self.silent_mode = False
        self.add_log("一键启动", "一键启动完成")
        self.one_click_button.configure(state="normal", text="一键启动")

        # 强制更新UI
        self.root.update()
    
    def on_closing(self):
        """窗口关闭时的处理"""
        if self.is_proxy_running:
            if messagebox.askokcancel("退出", "代理服务器正在运行，确定要退出吗？"):
                self.add_log("系统", "正在关闭程序...")
                self.root.destroy()
        else:
            self.root.destroy()

    def _powershell_injection(self, pid):
        """使用PowerShell作为中介执行注入 - 非阻塞版本"""
        try:
            proxy_address = self.proxy_address_entry.get().strip()
            if not proxy_address:
                proxy_address = "127.0.0.1:1080"

            self.root.after(0, lambda: self.add_log("PowerShell", f"使用PowerShell中介代理 PID: {pid} -> {proxy_address}"))

            # 构建PowerShell命令
            cli_path = get_resource_path("tools/proxinjector-cli.exe")
            ps_cmd = f"& '{cli_path}' -i {pid} -p {proxy_address} -l"

            self.root.after(0, lambda: self.add_log("PowerShell", f"执行: {ps_cmd}"))

            # 使用Popen启动PowerShell，不等待结束
            process = subprocess.Popen(
                ["powershell.exe", "-Command", ps_cmd],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.root.after(0, lambda: self.add_log("进程", f"PowerShell进程已启动，PID: {process.pid}"))

            # 等待几秒钟检查代理是否成功
            self.root.after(8000, lambda: self._simple_check_injection(process, pid, proxy_address))

        except Exception as e:
            self.root.after(0, lambda: self.add_log("异常", f"PowerShell代理异常: {e}"))
            self.root.after(0, lambda: self.inject_button.configure(state="normal", text="开始代理"))

    def _check_injection_success(self, process, pid, proxy_address):
        """检查注入是否成功 - 改进版本"""
        try:
            # 等待进程输出，但不阻塞太久
            try:
                # 使用非阻塞方式读取部分输出
                import select
                import time

                # 等待最多10秒看是否有输出
                start_time = time.time()
                output_lines = []

                while time.time() - start_time < 10:
                    if process.poll() is not None:
                        # 进程结束了，读取所有输出
                        stdout, stderr = process.communicate()
                        if stdout:
                            output_lines.extend(stdout.strip().split('\n'))
                        break

                    # 尝试读取部分输出
                    try:
                        # 检查是否有可读数据
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            # 检查是否包含代理成功的标志
                            if "injected" in line or "established injectee connection" in line:
                                self.root.after(0, lambda: self.add_log("检测", "发现代理成功标志"))
                                self.root.after(0, lambda: self.add_log("成功", f"代理成功! 代理地址: {proxy_address}"))
                                self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                                self.is_injected = True
                                self.injection_process = process
                                return
                    except:
                        pass

                    time.sleep(0.5)

                # 如果10秒后进程还在运行，假设代理成功
                if process.poll() is None:
                    self.root.after(0, lambda: self.add_log("检查", "进程持续运行，假设代理成功"))
                    self.root.after(0, lambda: self.add_log("成功", f"代理成功! 代理地址: {proxy_address}"))
                    self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                    self.is_injected = True
                    self.injection_process = process
                else:
                    # 进程结束了，检查返回码
                    if process.returncode == 0:
                        self.root.after(0, lambda: self.add_log("成功", "代理成功!"))
                        self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                        self.is_injected = True
                    else:
                        self.root.after(0, lambda: self.add_log("失败", f"代理失败，返回码: {process.returncode}"))
                        self.root.after(0, lambda: self.inject_button.configure(state="normal", text="开始代理"))

            except ImportError:
                # 如果没有select模块，使用简化逻辑
                time.sleep(5)
                if process.poll() is None:
                    self.root.after(0, lambda: self.add_log("成功", f"代理成功! 代理地址: {proxy_address}"))
                    self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                    self.is_injected = True
                    self.injection_process = process

        except Exception as e:
            self.root.after(0, lambda: self.add_log("异常", f"检查代理状态异常: {e}"))

    def _simple_check_injection(self, process, pid, proxy_address):
        """简化的代理检查"""
        try:
            # 如果8秒后进程还在运行，就认为代理成功
            if process.poll() is None:
                self.root.after(0, lambda: self.add_log("成功", f"代理成功! 代理地址: {proxy_address}"))
                self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                self.is_injected = True
                self.injection_process = process
                self.root.after(0, lambda: self.add_log("提示", "代理进程将持续运行"))
            else:
                # 进程已结束，检查返回码
                if hasattr(process, 'returncode') and process.returncode == 0:
                    self.root.after(0, lambda: self.add_log("成功", "代理成功!"))
                    self.root.after(0, lambda: self.injection_status.configure(text="🟢注入"))
                    self.is_injected = True
                else:
                    self.root.after(0, lambda: self.add_log("失败", "代理可能失败"))
                    self.root.after(0, lambda: self.inject_button.configure(state="normal", text="开始代理"))
        except Exception as e:
            self.root.after(0, lambda: self.add_log("异常", f"检查异常: {e}"))

    def _powershell_injection_direct(self, pid):
        """直接PowerShell注入 - 简化版本"""
        try:
            proxy_address = self.proxy_address_entry.get().strip()
            if not proxy_address:
                proxy_address = "127.0.0.1:1080"

            if not self.silent_mode:
                self.add_log("PowerShell", f"使用PowerShell中介代理 PID: {pid} -> {proxy_address}")

            # 构建PowerShell命令
            cli_path = get_resource_path("tools/proxinjector-cli.exe")
            ps_cmd = f"& '{cli_path}' -i {pid} -p {proxy_address} -l"
            if not self.silent_mode:
                self.add_log("PowerShell", f"执行: {ps_cmd}")

            # 启动PowerShell进程
            process = subprocess.Popen(
                ["powershell.exe", "-Command", ps_cmd],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if not self.silent_mode:
                self.add_log("进程", f"PowerShell进程已启动，PID: {process.pid}")

            # 2秒后标记成功
            self.root.after(2000, lambda: self._mark_success(proxy_address))

        except Exception as e:
            self.add_log("异常", f"PowerShell代理异常: {e}")
            self.inject_button.configure(state="normal", text="开始代理")

    def _mark_success(self, proxy_address):
        """标记代理成功"""
        try:
            self.add_log("成功", f"代理成功! 代理地址: {proxy_address}")
            self.is_injected = True
            self.inject_button.configure(state="normal", text="开始代理")
        except Exception as e:
            self.add_log("异常", f"标记成功状态异常: {e}")



    def create_platform_stats(self, parent):
        """创建各平台统计显示"""
        # 三叶草平台统计
        clover_frame = ctk.CTkFrame(parent)
        clover_frame.pack(side="left", fill="both", expand=True, padx=2, pady=5)

        ctk.CTkLabel(clover_frame, text="三叶草", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=(5, 2))
        self.clover_requests_label = ctk.CTkLabel(clover_frame, text="请求: 0", font=ctk.CTkFont(size=10))
        self.clover_requests_label.pack(pady=1)
        self.clover_success_label = ctk.CTkLabel(clover_frame, text="成功: 0", font=ctk.CTkFont(size=10))
        self.clover_success_label.pack(pady=(1, 5))

        # 熊猫平台统计
        panda_frame = ctk.CTkFrame(parent)
        panda_frame.pack(side="left", fill="both", expand=True, padx=2, pady=5)

        ctk.CTkLabel(panda_frame, text="熊猫", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=(5, 2))
        self.panda_requests_label = ctk.CTkLabel(panda_frame, text="请求: 0", font=ctk.CTkFont(size=10))
        self.panda_requests_label.pack(pady=1)
        self.panda_success_label = ctk.CTkLabel(panda_frame, text="成功: 0", font=ctk.CTkFont(size=10))
        self.panda_success_label.pack(pady=(1, 5))

        # 四海平台统计
        sihai_frame = ctk.CTkFrame(parent)
        sihai_frame.pack(side="left", fill="both", expand=True, padx=2, pady=5)

        ctk.CTkLabel(sihai_frame, text="四海", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=(5, 2))
        self.sihai_requests_label = ctk.CTkLabel(sihai_frame, text="请求: 0", font=ctk.CTkFont(size=10))
        self.sihai_requests_label.pack(pady=1)
        self.sihai_success_label = ctk.CTkLabel(sihai_frame, text="成功: 0", font=ctk.CTkFont(size=10))
        self.sihai_success_label.pack(pady=(1, 5))

    def create_total_stats(self, parent):
        """创建总计统计显示"""
        ctk.CTkLabel(parent, text="总计", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=(5, 2))

        stats_row = ctk.CTkFrame(parent)
        stats_row.pack(fill="x", padx=10, pady=(0, 5))

        self.total_requests_label = ctk.CTkLabel(stats_row, text="总请求: 0", font=ctk.CTkFont(size=11))
        self.total_requests_label.pack(side="left", padx=10)

        self.total_success_label = ctk.CTkLabel(stats_row, text="总成功: 0", font=ctk.CTkFont(size=11))
        self.total_success_label.pack(side="left", padx=10)

        self.success_rate_label = ctk.CTkLabel(stats_row, text="成功率: 0%", font=ctk.CTkFont(size=11))
        self.success_rate_label.pack(side="right", padx=10)

    def update_platform_stats(self, platform, stat_type):
        """更新平台统计"""
        if platform in self.platform_stats and stat_type in ['requests', 'success']:
            self.platform_stats[platform][stat_type] += 1
            self.refresh_stats_display()
            # 保存统计数据
            self.save_today_stats()

    def refresh_stats_display(self):
        """刷新统计显示"""
        # 更新各平台统计
        self.clover_requests_label.configure(text=f"请求: {self.platform_stats['clover']['requests']}")
        self.clover_success_label.configure(text=f"成功: {self.platform_stats['clover']['success']}")

        self.panda_requests_label.configure(text=f"请求: {self.platform_stats['panda']['requests']}")
        self.panda_success_label.configure(text=f"成功: {self.platform_stats['panda']['success']}")

        self.sihai_requests_label.configure(text=f"请求: {self.platform_stats['sihai']['requests']}")
        self.sihai_success_label.configure(text=f"成功: {self.platform_stats['sihai']['success']}")

        # 计算总计
        total_requests = sum(stats['requests'] for stats in self.platform_stats.values())
        total_success = sum(stats['success'] for stats in self.platform_stats.values())
        success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0

        # 更新总计显示
        self.total_requests_label.configure(text=f"总请求: {total_requests}")
        self.total_success_label.configure(text=f"总成功: {total_success}")
        self.success_rate_label.configure(text=f"成功率: {success_rate:.1f}%")

    def get_today_date(self):
        """获取今日日期字符串"""
        return datetime.now().strftime("%Y-%m-%d")

    def load_today_stats(self):
        """加载今日统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    all_stats = json.load(f)

                today = self.get_today_date()
                if today in all_stats:
                    self.platform_stats = all_stats[today]
                    self.refresh_stats_display()
                    self.add_log("统计", f"已加载今日统计数据")
                else:
                    self.add_log("统计", f"今日首次启动，统计数据已重置")
            else:
                self.add_log("统计", f"统计文件不存在，使用默认数据")
        except Exception as e:
            self.add_log("异常", f"加载统计数据失败: {e}")

    def save_today_stats(self):
        """保存今日统计数据"""
        try:
            # 读取现有数据
            all_stats = {}
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    all_stats = json.load(f)

            # 更新今日数据
            today = self.get_today_date()
            all_stats[today] = self.platform_stats.copy()

            # 保存数据
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(all_stats, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.add_log("异常", f"保存统计数据失败: {e}")

    def show_history_stats(self):
        """显示历史统计记录"""
        try:
            if not os.path.exists(self.stats_file):
                messagebox.showinfo("提示", "暂无历史统计数据")
                return

            with open(self.stats_file, 'r', encoding='utf-8') as f:
                all_stats = json.load(f)

            if not all_stats:
                messagebox.showinfo("提示", "暂无历史统计数据")
                return

            # 创建历史记录窗口
            self.create_history_window(all_stats)

        except Exception as e:
            messagebox.showerror("错误", f"读取历史数据失败: {e}")

    def create_history_window(self, all_stats):
        """创建历史记录窗口"""
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("历史统计记录")
        history_window.geometry("800x600")
        history_window.transient(self.root)
        history_window.grab_set()

        # 标题
        title_label = ctk.CTkLabel(
            history_window,
            text="历史统计记录",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=20)

        # 创建滚动框架
        scrollable_frame = ctk.CTkScrollableFrame(history_window)
        scrollable_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 按日期倒序排列
        sorted_dates = sorted(all_stats.keys(), reverse=True)

        for date in sorted_dates:
            stats = all_stats[date]
            self.create_date_stats_display(scrollable_frame, date, stats)

    def create_date_stats_display(self, parent, date, stats):
        """创建单日统计显示"""
        # 日期框架
        date_frame = ctk.CTkFrame(parent)
        date_frame.pack(fill="x", padx=10, pady=5)

        # 日期标题
        date_label = ctk.CTkLabel(
            date_frame,
            text=f"📅 {date}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        date_label.pack(pady=(10, 5))

        # 平台统计行
        platforms_frame = ctk.CTkFrame(date_frame)
        platforms_frame.pack(fill="x", padx=10, pady=5)

        # 三个平台的统计
        for platform, platform_name in [('clover', '三叶草'), ('panda', '熊猫'), ('sihai', '四海')]:
            platform_frame = ctk.CTkFrame(platforms_frame)
            platform_frame.pack(side="left", fill="both", expand=True, padx=2, pady=5)

            platform_label = ctk.CTkLabel(platform_frame, text=platform_name, font=ctk.CTkFont(size=11, weight="bold"))
            platform_label.pack(pady=(5, 2))

            requests = stats.get(platform, {}).get('requests', 0)
            success = stats.get(platform, {}).get('success', 0)

            requests_label = ctk.CTkLabel(platform_frame, text=f"请求: {requests}", font=ctk.CTkFont(size=10))
            requests_label.pack(pady=1)

            success_label = ctk.CTkLabel(platform_frame, text=f"成功: {success}", font=ctk.CTkFont(size=10))
            success_label.pack(pady=(1, 5))

        # 总计统计
        total_frame = ctk.CTkFrame(date_frame)
        total_frame.pack(fill="x", padx=10, pady=(5, 10))

        total_requests = sum(stats.get(platform, {}).get('requests', 0) for platform in ['clover', 'panda', 'sihai'])
        total_success = sum(stats.get(platform, {}).get('success', 0) for platform in ['clover', 'panda', 'sihai'])
        success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0

        total_label = ctk.CTkLabel(
            total_frame,
            text=f"总计 - 请求: {total_requests} | 成功: {total_success} | 成功率: {success_rate:.1f}%",
            font=ctk.CTkFont(size=11)
        )
        total_label.pack(pady=8)

    def validate_license(self):
        """验证许可证"""
        try:
            is_valid = validate_license()

            if not is_valid:
                # 显示许可证错误对话框
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                messagebox.showerror("许可证验证失败", "许可证验证失败，程序将退出。")
                root.destroy()
                return False

            return True

        except Exception as e:
            # 许可证验证异常
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("许可证验证异常", f"验证过程中发生错误: {e}\n\n程序将退出。")
            root.destroy()
            return False

    def run(self):
        """运行UI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ProxyUI()
        app.run()
    except ImportError:
        print("❌ 缺少CustomTkinter库，请安装：pip install customtkinter")
    except Exception as e:
        print(f"❌ 启动UI时出错: {e}")

if __name__ == "__main__":
    main()
