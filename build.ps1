# SoftProxy Build Script
# PowerShell Version

param(
    [switch]$Clean,
    [switch]$Test,
    [string]$Version = "1.0"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "SoftProxy Build Script v$Version" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check Python environment
Write-Host "Checking Python environment..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "OK $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR Python not found" -ForegroundColor Red
    exit 1
}

# Check Nuitka
Write-Host "Checking Nuitka..." -ForegroundColor Yellow
try {
    python -c "import nuitka" 2>$null
    Write-Host "OK Nuitka installed" -ForegroundColor Green
} catch {
    Write-Host "Installing Nuitka..." -ForegroundColor Yellow
    pip install nuitka
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR Nuitka installation failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "OK Nuitka installed successfully" -ForegroundColor Green
}

# Check required files
Write-Host "Checking required files..." -ForegroundColor Yellow
$requiredFiles = @("proxy_ui.py", "load_balancer.py", "socks5_load_balancer.py", "license_system.py", "tools/proxinjector-cli.exe")
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "OK $file" -ForegroundColor Green
    } else {
        Write-Host "ERROR Missing file: $file" -ForegroundColor Red
        exit 1
    }
}

# Clean old build files
if ($Clean -or (Test-Path "proxy_ui.dist") -or (Test-Path "proxy_ui.build") -or (Test-Path "dist")) {
    Write-Host "Cleaning old build files..." -ForegroundColor Yellow
    @("proxy_ui.dist", "proxy_ui.build", "dist") | ForEach-Object {
        if (Test-Path $_) {
            Remove-Item $_ -Recurse -Force
            Write-Host "OK Deleted $_" -ForegroundColor Green
        }
    }
}

# Create output directory
New-Item -ItemType Directory -Path "dist" -Force | Out-Null

# Start compilation
Write-Host "Starting Nuitka compilation..." -ForegroundColor Yellow
Write-Host "This may take several minutes, please wait..." -ForegroundColor Cyan

$nuitkaArgs = @(
    "--standalone"
    "--enable-plugin=tk-inter"
    "--windows-console-mode=disable"
    "--include-data-dir=tools=tools"
    "--include-data-files=daily_stats.json=daily_stats.json"
    "--include-data-files=requirements.txt=requirements.txt"
    "--include-data-files=README.md=README.md"
    "--include-data-files=license.dat=license.dat"
    "--output-dir=dist"
    "--remove-output"
    "--assume-yes-for-downloads"
    "proxy_ui.py"
)

$process = Start-Process -FilePath "nuitka" -ArgumentList $nuitkaArgs -Wait -PassThru -NoNewWindow
if ($process.ExitCode -ne 0) {
    Write-Host "ERROR Compilation failed" -ForegroundColor Red
    exit 1
}

Write-Host "OK Compilation successful" -ForegroundColor Green

# Rename output directory
if (Test-Path "dist/SoftProxy") {
    Remove-Item "dist/SoftProxy" -Recurse -Force
}
Rename-Item "dist/proxy_ui.dist" "SoftProxy"

# Copy required files
Write-Host "Copying required files..." -ForegroundColor Yellow
if (Test-Path "智赞助手.exe") {
    Copy-Item "智赞助手.exe" "dist/SoftProxy/" -Force
    Write-Host "OK 智赞助手.exe" -ForegroundColor Green
}

# Create version info
$versionInfo = @"
SoftProxy v$Version Production Release
Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Nuitka Compilation
Python Runtime Included
Build Machine: $env:COMPUTERNAME
"@
$versionInfo | Out-File "dist/SoftProxy/VERSION.txt" -Encoding UTF8

# Create usage instructions
$usageInfo = @"
SoftProxy Usage Instructions
============================

Quick Start:
1. Run proxy_ui.exe as Administrator
2. Ensure target software is in the same directory
3. Configure platform accounts in Account Management
4. Click One-Click Start to begin

Features:
- Home: One-Click Start and Today's Statistics
- Account Management: Configure platform accounts
- Platform Settings: Load balancer parameters
- History: View historical statistics

Notes:
- Administrator privileges required
- Configuration files created on first run
- Statistics automatically saved daily
- Supports multi-platform load balancing

Version: v$Version
Build Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@
$usageInfo | Out-File "dist/SoftProxy/README.txt" -Encoding UTF8

# Get file information
$exeSize = [math]::Round((Get-Item "dist/SoftProxy/proxy_ui.exe").Length / 1MB, 2)
$totalFiles = (Get-ChildItem "dist/SoftProxy" -Recurse -File).Count
$totalSize = [math]::Round((Get-ChildItem "dist/SoftProxy" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB, 2)

# Test run (optional)
if ($Test) {
    Write-Host "Testing application..." -ForegroundColor Yellow
    $testProcess = Start-Process -FilePath "dist/SoftProxy/proxy_ui.exe" -PassThru
    Start-Sleep 3
    if (!$testProcess.HasExited) {
        $testProcess.Kill()
        Write-Host "OK Application test passed" -ForegroundColor Green
    } else {
        Write-Host "ERROR Application test failed" -ForegroundColor Red
    }
}

# Display results
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Output directory: dist/SoftProxy/" -ForegroundColor White
Write-Host "Main executable: proxy_ui.exe ($exeSize MB)" -ForegroundColor White
Write-Host "Total files: $totalFiles" -ForegroundColor White
Write-Host "Total size: $totalSize MB" -ForegroundColor White
Write-Host "Version: v$Version" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Production environment ready!" -ForegroundColor Green
